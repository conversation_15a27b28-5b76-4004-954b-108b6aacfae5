<!DOCTYPE html>
<html>
<head>
    <title>Azure OCR App Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .success { background-color: #d4edda; color: #155724; }
        .error { background-color: #f8d7da; color: #721c24; }
        .info { background-color: #d1ecf1; color: #0c5460; }
        button {
            padding: 10px 20px;
            margin: 5px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            background-color: #007bff;
            color: white;
        }
        button:hover {
            background-color: #0056b3;
        }
    </style>
</head>
<body>
    <h1>🔍 Azure OCR App 测试页面</h1>
    
    <div class="info">
        <h3>📋 测试说明</h3>
        <p>这是一个测试页面，用于检查Flutter Web应用的状态。</p>
    </div>

    <div class="status" id="status">
        <h3>🔄 正在检查应用状态...</h3>
    </div>

    <h3>🌐 可能的访问地址：</h3>
    <ul>
        <li><a href="http://localhost:3000" target="_blank">http://localhost:3000</a> (Flutter默认端口)</li>
        <li><a href="http://localhost:8080" target="_blank">http://localhost:8080</a> (备用端口)</li>
        <li><a href="http://localhost:9000" target="_blank">http://localhost:9000</a> (静态服务器)</li>
        <li><a href="file:///C:/Project/002_HyperV_Azure_OCR/build/web/index.html" target="_blank">本地文件</a> (直接打开构建文件)</li>
    </ul>

    <h3>🛠️ 手动启动选项：</h3>
    <div>
        <button onclick="testUrl('http://localhost:3000')">测试 3000 端口</button>
        <button onclick="testUrl('http://localhost:8080')">测试 8080 端口</button>
        <button onclick="testUrl('http://localhost:9000')">测试 9000 端口</button>
    </div>

    <div id="test-results"></div>

    <h3>📝 手动启动命令：</h3>
    <div class="info">
        <p><strong>方法1 - Flutter开发服务器：</strong></p>
        <code>flutter run -d web-server --web-port 3000</code>
        
        <p><strong>方法2 - 使用批处理文件：</strong></p>
        <code>start_web.bat</code>
        
        <p><strong>方法3 - Python静态服务器：</strong></p>
        <code>cd build/web && python -m http.server 9000</code>
        
        <p><strong>方法4 - Node.js服务器：</strong></p>
        <code>npx http-server build/web -p 9000</code>
    </div>

    <script>
        function testUrl(url) {
            const resultsDiv = document.getElementById('test-results');
            resultsDiv.innerHTML += `<div class="info">正在测试 ${url}...</div>`;
            
            fetch(url)
                .then(response => {
                    if (response.ok) {
                        resultsDiv.innerHTML += `<div class="success">✅ ${url} 可访问！</div>`;
                        window.open(url, '_blank');
                    } else {
                        resultsDiv.innerHTML += `<div class="error">❌ ${url} 返回错误: ${response.status}</div>`;
                    }
                })
                .catch(error => {
                    resultsDiv.innerHTML += `<div class="error">❌ ${url} 无法访问: ${error.message}</div>`;
                });
        }

        // 自动检查常用端口
        window.onload = function() {
            const ports = [3000, 8080, 9000];
            const statusDiv = document.getElementById('status');
            let checkedPorts = 0;
            let foundPort = null;

            ports.forEach(port => {
                const url = `http://localhost:${port}`;
                fetch(url)
                    .then(response => {
                        checkedPorts++;
                        if (response.ok && !foundPort) {
                            foundPort = port;
                            statusDiv.innerHTML = `<div class="success">✅ 找到运行中的应用: <a href="${url}" target="_blank">${url}</a></div>`;
                        }
                        if (checkedPorts === ports.length && !foundPort) {
                            statusDiv.innerHTML = `<div class="error">❌ 未找到运行中的应用，请手动启动</div>`;
                        }
                    })
                    .catch(() => {
                        checkedPorts++;
                        if (checkedPorts === ports.length && !foundPort) {
                            statusDiv.innerHTML = `<div class="error">❌ 未找到运行中的应用，请手动启动</div>`;
                        }
                    });
            });
        };
    </script>
</body>
</html>
