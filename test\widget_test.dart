import 'package:flutter_test/flutter_test.dart';
import 'package:azure_ocr_app/main.dart';

void main() {
  testWidgets('App should start without errors', (WidgetTester tester) async {
    // Build our app and trigger a frame.
    await tester.pumpWidget(const MyApp());

    // Verify that the app title is displayed
    expect(find.text('Azure OCR 文字识别'), findsOneWidget);

    // Verify that the image placeholder is displayed
    expect(find.text('请选择图片'), findsOneWidget);

    // Verify that the camera and gallery buttons are present
    expect(find.text('拍照'), findsOneWidget);
    expect(find.text('相册'), findsOneWidget);

    // Verify that the OCR button is present but disabled initially
    expect(find.text('开始文字识别'), findsOneWidget);
  });
}
