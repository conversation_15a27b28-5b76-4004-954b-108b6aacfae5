@echo off
echo === Azure OCR Web版快速启动 ===
echo.

REM 检查.env文件
if not exist ".env" (
    echo ⚠ .env文件不存在，请先配置Azure信息
    echo 运行: copy .env.example .env
    echo 然后编辑.env文件
    pause
    exit /b 1
)

echo ✓ 检查.env文件...完成
echo.

echo 启动Web服务器...
echo 注意：file_picker警告可以忽略，不影响Web功能
echo.
echo 应用将在浏览器中打开: http://localhost:3000
echo 按 Ctrl+C 停止服务器
echo.

REM 使用不同端口避免权限问题
flutter run -d web-server --web-port 3000 --web-hostname localhost
