import 'package:flutter/foundation.dart';
import '../models/dish.dart';
import '../services/dish_database_service.dart';
import '../services/allergen_service.dart';

// 配料识别服务
class IngredientDetectionService {
  // 从菜名识别配料和过敏原
  static Future<AllergenDetectionResult> detectAllergens(
      String dishName) async {
    // 获取用户设置的过敏原（在try外面定义，确保catch块也能访问）
    final selectedAllergens = await AllergenService.getSelectedAllergens();
    final userAllergens = selectedAllergens.map((a) => a.name).toList();

    try {
      // 确保数据库已初始化
      await DishDatabaseService.initialize();

      // 1. 直接匹配数据库中的菜品（去除空格后匹配）
      final cleanDishName = dishName.replaceAll(' ', '');
      if (kDebugMode) {
        print('🔍 过敏原检测调试: 原始菜名="$dishName", 清理后="$cleanDishName"');
      }
      final exactMatches = DishDatabaseService.searchDishes(cleanDishName);

      if (exactMatches.isNotEmpty) {
        // 找到精确匹配的菜品
        final dish = exactMatches.first;
        if (kDebugMode) {
          print(
              '🔍 找到精确匹配菜品: ${dish.name}, 配料: ${dish.ingredients}, 过敏原: ${dish.allergens}');
        }
        final matchedAllergens =
            _findMatchedAllergens(dish.allergens, userAllergens);
        final riskLevel = _calculateRiskLevel(matchedAllergens, userAllergens);

        return AllergenDetectionResult(
          dishName: dishName,
          detectedIngredients: dish.ingredients,
          detectedAllergens: dish.allergens,
          userAllergens: userAllergens,
          matchedAllergens: matchedAllergens,
          riskLevel: riskLevel,
          warning: _generateWarning(matchedAllergens, riskLevel),
          suggestions: _generateSuggestions(matchedAllergens, dish.ingredients),
        );
      }

      // 2. 关键词匹配识别配料
      final detectedIngredients = _detectIngredientsByKeywords(dishName);
      final detectedAllergens = _getAllergensByIngredients(detectedIngredients);
      final matchedAllergens =
          _findMatchedAllergens(detectedAllergens, userAllergens);
      final riskLevel = _calculateRiskLevel(matchedAllergens, userAllergens);

      return AllergenDetectionResult(
        dishName: dishName,
        detectedIngredients: detectedIngredients,
        detectedAllergens: detectedAllergens,
        userAllergens: userAllergens,
        matchedAllergens: matchedAllergens,
        riskLevel: riskLevel,
        warning: _generateWarning(matchedAllergens, riskLevel),
        suggestions:
            _generateSuggestions(matchedAllergens, detectedIngredients),
      );
    } catch (e) {
      if (kDebugMode) {
        print('过敏原检测失败: $e');
      }

      // 返回未知风险结果
      return AllergenDetectionResult(
        dishName: dishName,
        detectedIngredients: [],
        detectedAllergens: [],
        userAllergens: userAllergens,
        matchedAllergens: [],
        riskLevel: AllergenRiskLevel.unknown,
        warning: '无法识别菜品配料，建议询问服务员确认是否含有过敏原',
        suggestions: ['询问服务员具体配料', '查看菜品详细说明', '选择其他确定安全的菜品'],
      );
    }
  }

  // 通过关键词匹配识别配料（增强版）
  static List<String> _detectIngredientsByKeywords(String dishName) {
    final ingredients = DishDatabaseService.getAllIngredients();
    final detectedIngredients = <String>[];
    final confidenceScores = <String, double>{};

    if (kDebugMode) {
      print('🔍 开始智能配料分析: "$dishName"');
    }

    for (var ingredient in ingredients) {
      double maxConfidence = 0.0;
      String? matchedKeyword;

      for (var keyword in ingredient.keywords) {
        final confidence =
            _calculateAdvancedKeywordConfidence(dishName, keyword);
        if (confidence > maxConfidence) {
          maxConfidence = confidence;
          matchedKeyword = keyword;
        }
      }

      // 降低置信度阈值，增加多级检测
      if (maxConfidence > 0.5) {
        detectedIngredients.add(ingredient.name);
        confidenceScores[ingredient.name] = maxConfidence;

        if (kDebugMode) {
          print(
              '  ✅ 高置信度检测: ${ingredient.name} (关键词: $matchedKeyword, 置信度: ${(maxConfidence * 100).toStringAsFixed(1)}%)');
        }
      } else if (maxConfidence > 0.3) {
        // 中等置信度也加入，但标记为可能
        detectedIngredients.add(ingredient.name);
        confidenceScores[ingredient.name] = maxConfidence;

        if (kDebugMode) {
          print(
              '  ⚠️ 中等置信度检测: ${ingredient.name} (关键词: $matchedKeyword, 置信度: ${(maxConfidence * 100).toStringAsFixed(1)}%)');
        }
      } else if (maxConfidence > 0.1) {
        if (kDebugMode) {
          print(
              '  💡 低置信度可能: ${ingredient.name} (关键词: $matchedKeyword, 置信度: ${(maxConfidence * 100).toStringAsFixed(1)}%)');
        }
      }
    }

    // 按置信度排序
    detectedIngredients.sort((a, b) =>
        (confidenceScores[b] ?? 0.0).compareTo(confidenceScores[a] ?? 0.0));

    if (kDebugMode) {
      print('  📊 最终检测结果: $detectedIngredients');
    }

    return detectedIngredients;
  }

  // 高级关键词匹配置信度计算（支持模糊匹配和同义词）
  static double _calculateAdvancedKeywordConfidence(
      String dishName, String keyword) {
    if (keyword.isEmpty) return 0.0;

    // 1. 精确匹配 - 最高分
    if (dishName.contains(keyword)) {
      // 完整词匹配得分更高
      if (dishName == keyword) {
        return 1.0;
      }

      // 考虑关键词在菜名中的位置和长度
      final keywordLength = keyword.length;
      final dishNameLength = dishName.length;

      // 长关键词匹配得分更高
      double lengthScore = keywordLength / dishNameLength.toDouble();
      lengthScore = lengthScore > 1.0 ? 1.0 : lengthScore;

      // 基础匹配分数
      double baseScore = 0.8;

      // 如果关键词在开头或结尾，得分更高
      if (dishName.startsWith(keyword) || dishName.endsWith(keyword)) {
        baseScore += 0.1;
      }

      return (baseScore + lengthScore * 0.2).clamp(0.0, 1.0);
    }

    // 2. 字符相似度匹配 - 中等分
    final similarity = _calculateChineseSimilarity(dishName, keyword);
    if (similarity > 0.6) {
      return (0.6 + similarity * 0.3).clamp(0.0, 0.9);
    }

    // 3. 部分字符匹配 - 较低分
    final partialMatch = _calculatePartialMatch(dishName, keyword);
    if (partialMatch > 0.5) {
      return (0.3 + partialMatch * 0.3).clamp(0.0, 0.6);
    }

    // 4. 同义词和变体匹配
    final synonymMatch = _checkSynonymMatch(dishName, keyword);
    if (synonymMatch > 0.0) {
      return synonymMatch;
    }

    return 0.0;
  }

  // 计算中文字符相似度
  static double _calculateChineseSimilarity(String s1, String s2) {
    if (s1.isEmpty || s2.isEmpty) return 0.0;
    if (s1 == s2) return 1.0;

    // 计算共同字符数
    int commonChars = 0;
    final chars1 = s1.split('');
    final chars2 = s2.split('');

    for (var char in chars1) {
      if (chars2.contains(char)) {
        commonChars++;
      }
    }

    // 相似度 = 共同字符数 / 较长字符串长度
    final maxLength = s1.length > s2.length ? s1.length : s2.length;
    return commonChars / maxLength;
  }

  // 计算部分匹配度
  static double _calculatePartialMatch(String dishName, String keyword) {
    if (keyword.length < 2) return 0.0;

    int matches = 0;
    for (int i = 0; i < keyword.length; i++) {
      if (dishName.contains(keyword[i])) {
        matches++;
      }
    }

    return matches / keyword.length;
  }

  // 检查同义词匹配
  static double _checkSynonymMatch(String dishName, String keyword) {
    // 定义常见的同义词和变体
    final synonyms = {
      '虾': ['虾仁', '明虾', '基围虾', '河虾', '海虾'],
      '蟹': ['螃蟹', '大闸蟹', '梭子蟹', '蟹肉', '蟹黄'],
      '鱼': ['鲈鱼', '草鱼', '鲤鱼', '带鱼', '黄花鱼', '鱼片', '鱼肉'],
      '花生': ['花生米', '花生仁'],
      '核桃': ['核桃仁', '胡桃'],
      '鸡蛋': ['鸡蛋', '蛋', '蛋液', '蛋白', '蛋黄'],
      '牛奶': ['奶', '乳', '奶制品'],
      '豆腐': ['豆干', '豆皮', '豆制品'],
    };

    // 检查关键词是否有同义词在菜名中
    if (synonyms.containsKey(keyword)) {
      for (var synonym in synonyms[keyword]!) {
        if (dishName.contains(synonym)) {
          return 0.7; // 同义词匹配给予较高分数
        }
      }
    }

    // 反向检查：菜名中的词是否是关键词的同义词
    for (var entry in synonyms.entries) {
      if (entry.value.contains(keyword)) {
        if (dishName.contains(entry.key)) {
          return 0.7;
        }
      }
    }

    return 0.0;
  }

  // 根据配料获取过敏原
  static List<String> _getAllergensByIngredients(List<String> ingredients) {
    final allIngredients = DishDatabaseService.getAllIngredients();
    final allergens = <String>[];

    for (var ingredientName in ingredients) {
      final ingredient = allIngredients.firstWhere(
        (ing) => ing.name == ingredientName,
        orElse: () => Ingredient(
          id: '',
          name: '',
          category: '',
          allergens: [],
          keywords: [],
        ),
      );

      if (ingredient.id.isNotEmpty) {
        allergens.addAll(ingredient.allergens);
      }
    }

    return allergens.toSet().toList(); // 去重
  }

  // 找到匹配的过敏原（增强版 - 支持模糊匹配和同义词）
  static List<String> _findMatchedAllergens(
      List<String> detectedAllergens, List<String> userAllergens) {
    if (kDebugMode) {
      print('🔍 过敏原匹配调试:');
      print('  检测到的过敏原: $detectedAllergens');
      print('  用户设置的过敏原: $userAllergens');
    }

    final matched = <String>[];

    for (var detectedAllergen in detectedAllergens) {
      bool isMatched = false;
      String? matchedUserAllergen;

      for (var userAllergen in userAllergens) {
        // 1. 精确匹配（忽略大小写）
        if (userAllergen.toLowerCase() == detectedAllergen.toLowerCase()) {
          isMatched = true;
          matchedUserAllergen = userAllergen;
          break;
        }

        // 2. 包含匹配（双向）
        if (userAllergen.contains(detectedAllergen) ||
            detectedAllergen.contains(userAllergen)) {
          isMatched = true;
          matchedUserAllergen = userAllergen;
          break;
        }

        // 3. 同义词匹配
        if (_areAllergensSynonyms(detectedAllergen, userAllergen)) {
          isMatched = true;
          matchedUserAllergen = userAllergen;
          break;
        }

        // 4. 模糊匹配（相似度）
        final similarity =
            _calculateChineseSimilarity(detectedAllergen, userAllergen);
        if (similarity > 0.8) {
          isMatched = true;
          matchedUserAllergen = userAllergen;
          break;
        }
      }

      if (isMatched) {
        matched.add(detectedAllergen);
        if (kDebugMode) {
          print('  ✅ 匹配: "$detectedAllergen" <-> "$matchedUserAllergen"');
        }
      } else {
        if (kDebugMode) {
          print('  ❌ 未匹配: "$detectedAllergen"');
        }
      }
    }

    if (kDebugMode) {
      print('  最终匹配结果: $matched');
    }

    return matched;
  }

  // 检查两个过敏原是否为同义词
  static bool _areAllergensSynonyms(String allergen1, String allergen2) {
    final allergenSynonyms = {
      '虾': ['虾类', '虾仁', '明虾', '基围虾', '河虾', '海虾', '虾制品'],
      '蟹': ['蟹类', '螃蟹', '大闸蟹', '梭子蟹', '蟹肉', '蟹黄', '蟹制品'],
      '鱼': ['鱼类', '鲈鱼', '草鱼', '鲤鱼', '带鱼', '黄花鱼', '鱼片', '鱼肉', '鱼制品'],
      '花生': ['花生类', '花生米', '花生仁', '花生制品', '花生油'],
      '核桃': ['核桃类', '核桃仁', '胡桃', '核桃制品'],
      '杏仁': ['杏仁类', '杏仁仁', '杏仁制品'],
      '鸡蛋': ['蛋类', '蛋', '蛋液', '蛋白', '蛋黄', '蛋制品'],
      '牛奶': ['奶类', '乳制品', '奶', '乳', '牛乳', '鲜奶'],
      '奶酪': ['芝士', '乳酪', '起司', '奶酪制品'],
      '大豆': ['豆类', '黄豆', '豆制品', '豆腐', '豆浆'],
      '小麦': ['麦类', '面粉', '面制品', '小麦制品'],
      '芝麻': ['芝麻类', '芝麻油', '芝麻制品'],
      '味精': ['MSG', '谷氨酸钠', '鸡精'],
    };

    // 检查是否为直接同义词
    for (var entry in allergenSynonyms.entries) {
      final mainAllergen = entry.key;
      final synonyms = entry.value;

      if ((allergen1 == mainAllergen && synonyms.contains(allergen2)) ||
          (allergen2 == mainAllergen && synonyms.contains(allergen1)) ||
          (synonyms.contains(allergen1) && synonyms.contains(allergen2))) {
        return true;
      }
    }

    return false;
  }

  // 计算风险等级（增强版）
  static AllergenRiskLevel _calculateRiskLevel(
      List<String> matchedAllergens, List<String> userAllergens) {
    if (matchedAllergens.isEmpty) {
      return AllergenRiskLevel.safe;
    }

    if (kDebugMode) {
      print('🔍 开始风险等级计算:');
      print('  匹配的过敏原: $matchedAllergens');
    }

    // 定义过敏原严重程度等级
    final Map<String, int> allergenSeverity = {
      // 高风险过敏原 (严重程度: 3)
      '花生': 3, '虾': 3, '蟹': 3, '牛奶': 3, '鸡蛋': 3,
      // 中风险过敏原 (严重程度: 2)
      '鱼': 2, '大豆': 2, '小麦': 2, '核桃': 2, '杏仁': 2,
      // 低风险过敏原 (严重程度: 1)
      '芝麻': 1, '味精': 1, '奶酪': 1,
    };

    // 计算总风险分数
    int totalRiskScore = 0;
    int highSeverityCount = 0;
    int mediumSeverityCount = 0;

    for (var allergen in matchedAllergens) {
      final severity = allergenSeverity[allergen] ?? 1;
      totalRiskScore += severity;

      if (severity >= 3) {
        highSeverityCount++;
      } else if (severity >= 2) {
        mediumSeverityCount++;
      }

      if (kDebugMode) {
        print('  过敏原: $allergen, 严重程度: $severity');
      }
    }

    if (kDebugMode) {
      print('  总风险分数: $totalRiskScore');
      print('  高严重程度过敏原数量: $highSeverityCount');
      print('  中严重程度过敏原数量: $mediumSeverityCount');
    }

    // 基于综合评估确定风险等级
    AllergenRiskLevel riskLevel;

    if (highSeverityCount >= 2 || totalRiskScore >= 8) {
      // 多个高风险过敏原或总分很高
      riskLevel = AllergenRiskLevel.high;
    } else if (highSeverityCount >= 1 ||
        totalRiskScore >= 5 ||
        matchedAllergens.length >= 3) {
      // 有高风险过敏原或中等总分或过敏原数量多
      riskLevel = AllergenRiskLevel.medium;
    } else if (totalRiskScore >= 2 || matchedAllergens.length >= 2) {
      // 有一定风险
      riskLevel = AllergenRiskLevel.low;
    } else {
      // 风险较小
      riskLevel = AllergenRiskLevel.low;
    }

    if (kDebugMode) {
      print('  最终风险等级: ${riskLevel.displayName}');
    }

    return riskLevel;
  }

  // 生成警告信息
  static String _generateWarning(
      List<String> matchedAllergens, AllergenRiskLevel riskLevel) {
    if (matchedAllergens.isEmpty) {
      return '未检测到您设置的过敏原，相对安全';
    }

    final allergenList = matchedAllergens.join('、');

    switch (riskLevel) {
      case AllergenRiskLevel.low:
        return '检测到可能含有：$allergenList，请谨慎食用';
      case AllergenRiskLevel.medium:
        return '检测到含有：$allergenList，不建议食用';
      case AllergenRiskLevel.high:
        return '检测到多种过敏原：$allergenList，强烈不建议食用';
      default:
        return '检测到过敏原：$allergenList，请注意';
    }
  }

  // 生成建议
  static List<String> _generateSuggestions(
      List<String> matchedAllergens, List<String> ingredients) {
    final suggestions = <String>[];

    if (matchedAllergens.isEmpty) {
      suggestions.add('该菜品相对安全，可以食用');
      suggestions.add('如有疑虑，可询问服务员确认配料');
    } else {
      suggestions.add('避免食用此菜品');
      suggestions.add('询问服务员是否可以去除过敏原配料');
      suggestions.add('选择其他不含过敏原的菜品');

      // 根据具体过敏原给出建议
      if (matchedAllergens.contains('花生')) {
        suggestions.add('注意花生油也可能引起过敏反应');
      }
      if (matchedAllergens.contains('虾') || matchedAllergens.contains('蟹')) {
        suggestions.add('海鲜过敏者应避免所有海鲜类菜品');
      }
      if (matchedAllergens.contains('牛奶')) {
        suggestions.add('注意奶油、黄油等乳制品也需避免');
      }
    }

    return suggestions;
  }

  // 获取推荐的安全菜品
  static List<Dish> getRecommendedSafeDishes(List<String> userAllergens) {
    final allDishes = DishDatabaseService.getAllDishes();

    return allDishes
        .where((dish) {
          // 检查菜品是否不含用户过敏原
          return !dish.allergens.any((allergen) => userAllergens.any(
              (userAllergen) =>
                  userAllergen.toLowerCase() == allergen.toLowerCase()));
        })
        .take(5)
        .toList(); // 返回前5个安全菜品
  }

  // 批量检测多个菜品（增强版）
  static Future<List<AllergenDetectionResult>> batchDetectAllergens(
      List<String> dishNames) async {
    if (kDebugMode) {
      print('🍽️ 开始批量检测 ${dishNames.length} 道菜品');
    }

    final results = <AllergenDetectionResult>[];

    for (int i = 0; i < dishNames.length; i++) {
      final dishName = dishNames[i];
      if (kDebugMode) {
        print('  检测第 ${i + 1}/${dishNames.length} 道菜: $dishName');
      }

      final result = await detectAllergens(dishName);
      results.add(result);
    }

    // 生成批量检测摘要
    if (kDebugMode) {
      final safeCount =
          results.where((r) => r.riskLevel == AllergenRiskLevel.safe).length;
      final lowRiskCount =
          results.where((r) => r.riskLevel == AllergenRiskLevel.low).length;
      final mediumRiskCount =
          results.where((r) => r.riskLevel == AllergenRiskLevel.medium).length;
      final highRiskCount =
          results.where((r) => r.riskLevel == AllergenRiskLevel.high).length;
      final unknownCount =
          results.where((r) => r.riskLevel == AllergenRiskLevel.unknown).length;

      print('📊 批量检测摘要:');
      print('  安全菜品: $safeCount 道');
      print('  低风险菜品: $lowRiskCount 道');
      print('  中风险菜品: $mediumRiskCount 道');
      print('  高风险菜品: $highRiskCount 道');
      print('  未知风险菜品: $unknownCount 道');
    }

    return results;
  }

  // 智能菜品提取（从OCR文本中提取多个菜品名称）
  static List<String> extractDishNames(List<String> ocrLines) {
    final dishNames = <String>[];

    // 强制输出调试信息（Web版本）
    print('🔍 开始智能菜品提取，共 ${ocrLines.length} 行文本');
    print('📝 原始OCR文本:');
    for (int i = 0; i < ocrLines.length; i++) {
      print(
          '  第${i + 1}行: "${ocrLines[i]}" (字节长度: ${ocrLines[i].codeUnits.length})');
    }

    for (int i = 0; i < ocrLines.length; i++) {
      final line = ocrLines[i];
      final cleanLine = _cleanOcrText(line);

      print(
          '  检查第${i + 1}行: "$line" -> "$cleanLine" (长度: ${cleanLine.length})');

      // 显示字符编码信息
      if (line.isNotEmpty) {
        print('    原始字符编码: ${line.codeUnits}');
        print('    清洗后字符编码: ${cleanLine.codeUnits}');
      }

      // 放宽长度限制
      if (cleanLine.length < 2 || cleanLine.length > 20) {
        print('    ❌ 长度不符合要求');
        continue;
      }

      // 过滤掉明显不是菜品的文本
      if (_isLikelyDishName(cleanLine)) {
        dishNames.add(cleanLine);
        print('  ✅ 提取菜品: $cleanLine');

        // 立即测试数据库匹配
        print('    🔍 立即测试数据库匹配:');
        print('    🔍 OCR清洗结果: "$cleanLine"');

        // 使用与数据库一致的清洗方式进行匹配
        final dbCleanLine = _cleanForDatabaseSearch(cleanLine);
        print('    🔍 数据库清洗结果: "$dbCleanLine"');

        final testMatches = DishDatabaseService.searchDishes(cleanLine);
        if (testMatches.isNotEmpty) {
          print('    ✅ 找到匹配: ${testMatches.map((d) => d.name).toList()}');
        } else {
          print('    ❌ 未找到匹配，尝试直接用数据库清洗结果搜索...');
          final testMatches2 = DishDatabaseService.searchDishes(dbCleanLine);
          if (testMatches2.isNotEmpty) {
            print(
                '    ✅ 数据库清洗后找到匹配: ${testMatches2.map((d) => d.name).toList()}');
          } else {
            print('    ❌ 仍未找到匹配');
          }
        }
      } else {
        print('    ❌ 不像菜品名称');
      }
    }

    // 去重但不限制数量（提取所有识别到的菜品）
    final uniqueDishNames = dishNames.toSet().toList();

    print('📋 最终提取到 ${uniqueDishNames.length} 道菜品: $uniqueDishNames');

    return uniqueDishNames;
  }

  // 清洗OCR识别的文本（修复版本 - 保留菜品名称中的重要字符）
  static String _cleanOcrText(String text) {
    String cleaned = text;

    print('    🧹 文字清洗: "$text"');

    // 1. 去除首尾空格
    cleaned = cleaned.trim();

    // 2. 去除多余空格，但保留单个空格
    cleaned = cleaned.replaceAll(RegExp(r'\s+'), ' ');

    // 3. 去除常见的中文标点符号
    cleaned =
        cleaned.replaceAll(RegExp(r'[，。、；：！？""' '（）【】《》〈〉「」『』〔〕［］｛｝]'), '');

    // 4. 去除英文标点符号
    cleaned = cleaned.replaceAll(RegExp(r'[,.;:!?"\(\)\[\]{}]'), '');

    // 5. 只去除价格相关符号，保留菜品名称中可能的数字
    cleaned = cleaned.replaceAll(RegExp(r'[￥¥元兀]'), '');

    // 去除明显的价格数字（如 "18.5" "25" 等独立的数字）
    // 但保留菜品名称中的数字（如"三杯鸡"中的"三"）
    cleaned = cleaned.replaceAll(RegExp(r'\b\d+\.?\d*\b'), '');

    // 6. 去除其他特殊符号
    cleaned = cleaned.replaceAll(RegExp(r'[-_=+|\\\/\*&%#@\$\^~`]'), '');

    // 7. 处理常见的OCR识别错误
    final ocrCorrections = {
      '兀': '元', // 常见OCR错误
      '鸡': '鸡', // 确保正确的中文字符
      '保': '保',
      '宫': '宫',
      '丁': '丁',
      '麻': '麻',
      '婆': '婆',
      '豆': '豆',
      '腐': '腐',
      '鱼': '鱼',
      '虾': '虾',
      '蟹': '蟹',
    };

    ocrCorrections.forEach((wrong, correct) {
      cleaned = cleaned.replaceAll(wrong, correct);
    });

    // 8. 最终去除多余空格
    cleaned = cleaned.trim().replaceAll(RegExp(r'\s+'), '');

    print('    🧹 清洗结果: "$cleaned"');
    return cleaned;
  }

  // 使用与数据库一致的清洗方式（用于匹配测试）
  static String _cleanForDatabaseSearch(String text) {
    String cleaned = text.toLowerCase().trim();

    // 去除空格
    cleaned = cleaned.replaceAll(' ', '');

    // 去除标点符号
    cleaned =
        cleaned.replaceAll(RegExp(r'[，。、；：！？""' '（）【】《》〈〉「」『』〔〕［］｛｝]'), '');
    cleaned = cleaned.replaceAll(RegExp(r'[,.;:!?"\(\)\[\]{}]'), '');

    // 去除数字和价格符号
    cleaned = cleaned.replaceAll(RegExp(r'[0-9￥¥元兀]'), '');

    return cleaned;
  }

  // 判断文本是否像菜品名称（超级宽松版本）
  static bool _isLikelyDishName(String text) {
    print('    🔍 检查是否为菜品: "$text"');

    // 只过滤掉最明显的非菜品内容
    if (RegExp(r'^\d+\.?\d*$').hasMatch(text)) {
      print('      ❌ 纯数字');
      return false;
    }

    // 过滤掉明显的价格行
    if (RegExp(r'^\d+\.?\d*元$').hasMatch(text)) {
      print('      ❌ 价格格式');
      return false;
    }

    // 超级宽松的长度限制
    if (text.length < 2) {
      print('      ❌ 太短 (${text.length})');
      return false;
    }

    // 过滤掉明显的非菜品词汇
    final nonDishKeywords = [
      '菜单',
      '价格',
      '荤菜',
      '素菜',
      '家常菜',
      '特色菜',
      '招牌菜',
      '热菜',
      '凉菜',
      '主食',
      '汤类',
      '饮品',
      '酒水',
      '小食',
      '元',
      '￥',
      '¥',
      '价',
      '费',
      '收费',
      '免费'
    ];

    bool hasNonDishKeyword =
        nonDishKeywords.any((keyword) => text.contains(keyword));
    if (hasNonDishKeyword) {
      print('      ❌ 包含非菜品关键词');
      return false;
    }

    // 包含中文字符就认为可能是菜品（超级宽松）
    bool hasChinese = RegExp(r'[\u4e00-\u9fa5]').hasMatch(text);

    if (hasChinese) {
      print('      ✅ 包含中文，认为是菜品候选');
      return true;
    }

    // 如果没有中文，检查是否有菜品相关的英文或其他字符
    if (text.length >= 3) {
      print('      ✅ 长度足够，认为是菜品候选');
      return true;
    }

    print('      ❌ 不像菜品');
    return false;
  }
}
