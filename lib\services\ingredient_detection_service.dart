import 'package:flutter/foundation.dart';
import '../models/dish.dart';
import '../services/dish_database_service.dart';
import '../services/allergen_service.dart';
import '../services/multilingual_text_processor.dart';
import '../services/image_preprocessing_service.dart';
import '../services/structured_data_service.dart';
import '../services/llm_ingredient_service.dart';
import '../services/multilingual_allergen_service.dart';
import 'dart:typed_data';

// 配料识别服务
class IngredientDetectionService {
  // 从菜名识别配料和过敏原（LLM增强版）
  static Future<AllergenDetectionResult> detectAllergens(
      String dishName) async {
    // 获取用户设置的过敏原（在try外面定义，确保catch块也能访问）
    final selectedAllergens = await AllergenService.getSelectedAllergens();
    final userAllergens = selectedAllergens.map((a) => a.name).toList();

    try {
      // 确保数据库已初始化
      DishDatabaseService.initialize();

      // 检测语言
      final language = _detectLanguage(dishName);
      if (kDebugMode) {
        print('🔍 过敏原检测调试: 原始菜名="$dishName", 检测语言="$language"');
      }

      // 1. 直接匹配数据库中的菜品（去除空格后匹配）
      final cleanDishName = dishName.replaceAll(' ', '');
      final exactMatches = DishDatabaseService.searchDishes(cleanDishName);

      if (exactMatches.isNotEmpty) {
        // 找到精确匹配的菜品
        final dish = exactMatches.first;
        if (kDebugMode) {
          print(
              '🔍 找到精确匹配菜品: ${dish.name}, 配料: ${dish.ingredients}, 过敏原: ${dish.allergens}');
        }
        final matchedAllergens =
            _findMatchedAllergens(dish.allergens, userAllergens);
        final riskLevel = _calculateRiskLevel(matchedAllergens, userAllergens);

        return AllergenDetectionResult(
          dishName: dishName,
          detectedIngredients: dish.ingredients,
          detectedAllergens: dish.allergens,
          userAllergens: userAllergens,
          matchedAllergens: matchedAllergens,
          riskLevel: riskLevel,
          warning: _generateWarning(matchedAllergens, riskLevel),
          suggestions: _generateSuggestions(matchedAllergens, dish.ingredients),
          detectionSource: 'database', // 添加检测来源
        );
      }

      // 2. 🆕 优先尝试LLM增强检测（非中文菜品）
      if (language != 'zh') {
        if (kDebugMode) {
          print('🤖 非中文菜品，尝试LLM增强检测');
        }

        try {
          final llmResult = await detectIngredientsWithLLM(dishName, language);

          if (llmResult['success'] && llmResult['confidence'] > 0.6) {
            if (kDebugMode) {
              print('✅ LLM检测成功: ${llmResult['llm_source']}');
              print('📋 LLM配料: ${llmResult['ingredients']}');
            }

            // 使用多语言过敏原检测
            final detectedAllergenTypes =
                MultilingualAllergenService.detectAllergensFromIngredients(
                    List<String>.from(llmResult['ingredients']), language);

            // 匹配用户过敏原
            final matchedAllergens =
                MultilingualAllergenService.findMatchedAllergens(
                    detectedAllergenTypes, userAllergens, 'zh' // 用户过敏原设置是中文
                    );

            final riskLevel =
                _calculateRiskLevel(matchedAllergens, userAllergens);
            final warning = MultilingualAllergenService.generateWarning(
                matchedAllergens, language);

            return AllergenDetectionResult(
              dishName: dishName,
              detectedIngredients: List<String>.from(llmResult['ingredients']),
              detectedAllergens: detectedAllergenTypes,
              userAllergens: userAllergens,
              matchedAllergens: matchedAllergens,
              riskLevel: riskLevel,
              warning: warning,
              suggestions: _generateSuggestions(matchedAllergens,
                  List<String>.from(llmResult['ingredients'])),
              detectionSource: 'llm_${llmResult['llm_source']}', // 显示LLM来源
            );
          }
        } catch (e) {
          if (kDebugMode) {
            print('❌ LLM检测失败: $e，回退到硬编码');
          }
        }
      }

      // 3. 回退到关键词匹配识别配料
      final detectedIngredients = _detectIngredientsByKeywords(dishName);
      final detectedAllergens = _getAllergensByIngredients(detectedIngredients);
      final matchedAllergens =
          _findMatchedAllergens(detectedAllergens, userAllergens);
      final riskLevel = _calculateRiskLevel(matchedAllergens, userAllergens);

      return AllergenDetectionResult(
        dishName: dishName,
        detectedIngredients: detectedIngredients,
        detectedAllergens: detectedAllergens,
        userAllergens: userAllergens,
        matchedAllergens: matchedAllergens,
        riskLevel: riskLevel,
        warning: _generateWarning(matchedAllergens, riskLevel),
        suggestions:
            _generateSuggestions(matchedAllergens, detectedIngredients),
        detectionSource: 'hardcoded_keywords', // 硬编码关键词
      );
    } catch (e) {
      if (kDebugMode) {
        print('过敏原检测失败: $e');
      }

      // 返回未知风险结果
      return AllergenDetectionResult(
        dishName: dishName,
        detectedIngredients: [],
        detectedAllergens: [],
        userAllergens: userAllergens,
        matchedAllergens: [],
        riskLevel: AllergenRiskLevel.unknown,
        warning: '无法识别菜品配料，建议询问服务员确认是否含有过敏原',
        suggestions: ['询问服务员具体配料', '查看菜品详细说明', '选择其他确定安全的菜品'],
        detectionSource: 'error', // 错误状态
      );
    }
  }

  // 通过关键词匹配识别配料（超级增强版）
  static List<String> _detectIngredientsByKeywords(String dishName) {
    final ingredients = DishDatabaseService.getAllIngredients();
    final detectedIngredients = <String>[];
    final confidenceScores = <String, double>{};

    if (kDebugMode) {
      print('🔍 开始智能配料分析: "$dishName"');
    }

    // 🆕 首先尝试基于菜名直接推断配料
    final directIngredients = _inferIngredientsFromDishName(dishName);
    for (var ingredient in directIngredients) {
      if (!detectedIngredients.contains(ingredient)) {
        detectedIngredients.add(ingredient);
        confidenceScores[ingredient] = 0.9; // 直接推断给高分
        if (kDebugMode) {
          print('  🎯 直接推断配料: $ingredient (置信度: 90%)');
        }
      }
    }

    for (var ingredient in ingredients) {
      double maxConfidence = 0.0;
      String? matchedKeyword;

      for (var keyword in ingredient.keywords) {
        final confidence =
            _calculateAdvancedKeywordConfidence(dishName, keyword);
        if (confidence > maxConfidence) {
          maxConfidence = confidence;
          matchedKeyword = keyword;
        }
      }

      // 🔧 大幅降低置信度阈值，增加检测覆盖率
      if (maxConfidence > 0.2) {
        // 降低到0.2，增加检测覆盖率
        detectedIngredients.add(ingredient.name);
        confidenceScores[ingredient.name] = maxConfidence;

        if (kDebugMode) {
          if (maxConfidence > 0.5) {
            print(
                '  ✅ 高置信度检测: ${ingredient.name} (关键词: $matchedKeyword, 置信度: ${(maxConfidence * 100).toStringAsFixed(1)}%)');
          } else if (maxConfidence > 0.3) {
            print(
                '  ⚠️ 中等置信度检测: ${ingredient.name} (关键词: $matchedKeyword, 置信度: ${(maxConfidence * 100).toStringAsFixed(1)}%)');
          } else {
            print(
                '  💡 低置信度检测: ${ingredient.name} (关键词: $matchedKeyword, 置信度: ${(maxConfidence * 100).toStringAsFixed(1)}%)');
          }
        }
      } else if (maxConfidence > 0.1) {
        if (kDebugMode) {
          print(
              '  🔍 极低置信度可能: ${ingredient.name} (关键词: $matchedKeyword, 置信度: ${(maxConfidence * 100).toStringAsFixed(1)}%)');
        }
      }
    }

    // 按置信度排序
    detectedIngredients.sort((a, b) =>
        (confidenceScores[b] ?? 0.0).compareTo(confidenceScores[a] ?? 0.0));

    if (kDebugMode) {
      print('  📊 最终检测结果: $detectedIngredients');
    }

    return detectedIngredients;
  }

  // 高级关键词匹配置信度计算（支持模糊匹配和同义词）
  static double _calculateAdvancedKeywordConfidence(
      String dishName, String keyword) {
    if (keyword.isEmpty) return 0.0;

    // 1. 精确匹配 - 最高分
    if (dishName.contains(keyword)) {
      // 完整词匹配得分更高
      if (dishName == keyword) {
        return 1.0;
      }

      // 考虑关键词在菜名中的位置和长度
      final keywordLength = keyword.length;
      final dishNameLength = dishName.length;

      // 长关键词匹配得分更高
      double lengthScore = keywordLength / dishNameLength.toDouble();
      lengthScore = lengthScore > 1.0 ? 1.0 : lengthScore;

      // 基础匹配分数
      double baseScore = 0.8;

      // 如果关键词在开头或结尾，得分更高
      if (dishName.startsWith(keyword) || dishName.endsWith(keyword)) {
        baseScore += 0.1;
      }

      return (baseScore + lengthScore * 0.2).clamp(0.0, 1.0);
    }

    // 2. 字符相似度匹配 - 中等分
    final similarity = _calculateChineseSimilarity(dishName, keyword);
    if (similarity > 0.6) {
      return (0.6 + similarity * 0.3).clamp(0.0, 0.9);
    }

    // 3. 部分字符匹配 - 较低分
    final partialMatch = _calculatePartialMatch(dishName, keyword);
    if (partialMatch > 0.5) {
      return (0.3 + partialMatch * 0.3).clamp(0.0, 0.6);
    }

    // 4. 同义词和变体匹配
    final synonymMatch = _checkSynonymMatch(dishName, keyword);
    if (synonymMatch > 0.0) {
      return synonymMatch;
    }

    return 0.0;
  }

  // 计算中文字符相似度
  static double _calculateChineseSimilarity(String s1, String s2) {
    if (s1.isEmpty || s2.isEmpty) return 0.0;
    if (s1 == s2) return 1.0;

    // 计算共同字符数
    int commonChars = 0;
    final chars1 = s1.split('');
    final chars2 = s2.split('');

    for (var char in chars1) {
      if (chars2.contains(char)) {
        commonChars++;
      }
    }

    // 相似度 = 共同字符数 / 较长字符串长度
    final maxLength = s1.length > s2.length ? s1.length : s2.length;
    return commonChars / maxLength;
  }

  // 计算部分匹配度
  static double _calculatePartialMatch(String dishName, String keyword) {
    if (keyword.length < 2) return 0.0;

    int matches = 0;
    for (int i = 0; i < keyword.length; i++) {
      if (dishName.contains(keyword[i])) {
        matches++;
      }
    }

    return matches / keyword.length;
  }

  // 检查同义词匹配（大幅增强版）
  static double _checkSynonymMatch(String dishName, String keyword) {
    // 🔧 大幅扩展同义词和变体库
    final synonyms = {
      // 海鲜类
      '虾': ['虾仁', '明虾', '基围虾', '河虾', '海虾', '白虾', '青虾', '龙虾'],
      '蟹': ['螃蟹', '大闸蟹', '梭子蟹', '蟹肉', '蟹黄', '河蟹', '海蟹'],
      '鱼': [
        '鲈鱼',
        '草鱼',
        '鲤鱼',
        '带鱼',
        '黄花鱼',
        '鱼片',
        '鱼肉',
        '鲫鱼',
        '鳕鱼',
        '三文鱼',
        '金枪鱼'
      ],

      // 坚果类
      '花生': ['花生米', '花生仁', '花生油'],
      '核桃': ['核桃仁', '胡桃'],
      '杏仁': ['杏仁仁', '美国大杏仁'],

      // 蛋奶类
      '鸡蛋': ['鸡蛋', '蛋', '蛋液', '蛋白', '蛋黄', '鸡蛋清', '蛋花'],
      '牛奶': ['奶', '乳', '奶制品', '牛乳', '鲜奶'],
      '奶酪': ['芝士', '乳酪', '起司'],

      // 豆制品
      '豆腐': ['豆干', '豆皮', '豆制品', '内酯豆腐', '老豆腐', '嫩豆腐'],
      '大豆': ['黄豆', '豆浆', '豆奶'],

      // 肉类
      '猪肉': ['猪', '五花肉', '里脊肉', '猪排', '猪蹄', '猪肚'],
      '牛肉': ['牛', '牛排', '牛腩', '牛筋'],
      '鸡肉': ['鸡', '鸡胸肉', '鸡腿', '鸡翅', '鸡丁', '口水鸡', '白切鸡'],
      '羊肉': ['羊', '羊排', '羊腿'],

      // 蔬菜类
      '白菜': ['大白菜', '小白菜', '娃娃菜'],
      '菠菜': ['菠菜叶'],
      '韭菜': ['韭黄', '韭菜花'],
      '萝卜': ['白萝卜', '胡萝卜', '青萝卜'],
      '土豆': ['马铃薯', '洋芋', '薯条', '土豆丝'],
      '茄子': ['紫茄子', '长茄子'],
      '辣椒': ['青椒', '红椒', '尖椒', '甜椒', '小米椒'],

      // 调料类
      '生抽': ['酱油', '老抽', '蒸鱼豉油'],
      '料酒': ['黄酒', '绍兴酒'],
      '香油': ['芝麻油', '麻油'],
      '胡椒': ['黑胡椒', '白胡椒'],
    };

    // 检查关键词是否有同义词在菜名中
    if (synonyms.containsKey(keyword)) {
      for (var synonym in synonyms[keyword]!) {
        if (dishName.contains(synonym)) {
          return 0.7; // 同义词匹配给予较高分数
        }
      }
    }

    // 反向检查：菜名中的词是否是关键词的同义词
    for (var entry in synonyms.entries) {
      if (entry.value.contains(keyword)) {
        if (dishName.contains(entry.key)) {
          return 0.7;
        }
      }
    }

    return 0.0;
  }

  // 🆕 基于菜名直接推断配料（智能规则）
  static List<String> _inferIngredientsFromDishName(String dishName) {
    final ingredients = <String>[];

    // 海鲜类菜品推断
    if (dishName.contains('虾') ||
        dishName.contains('基围虾') ||
        dishName.contains('明虾')) {
      ingredients.add('虾');
    }
    if (dishName.contains('蟹') ||
        dishName.contains('螃蟹') ||
        dishName.contains('大闸蟹')) {
      ingredients.add('蟹');
    }
    if (dishName.contains('鱼') ||
        dishName.contains('鲈鱼') ||
        dishName.contains('草鱼') ||
        dishName.contains('带鱼') ||
        dishName.contains('黄花鱼')) {
      ingredients.add('鱼');
    }

    // 肉类推断
    if (dishName.contains('鸡') ||
        dishName.contains('鸡肉') ||
        dishName.contains('鸡丁') ||
        dishName.contains('口水鸡') ||
        dishName.contains('白切鸡')) {
      ingredients.add('鸡肉');
    }
    if (dishName.contains('猪') ||
        dishName.contains('猪肉') ||
        dishName.contains('五花肉') ||
        dishName.contains('里脊') ||
        dishName.contains('猪排')) {
      ingredients.add('猪肉');
    }
    if (dishName.contains('牛') ||
        dishName.contains('牛肉') ||
        dishName.contains('牛排') ||
        dishName.contains('牛腩')) {
      ingredients.add('牛肉');
    }
    if (dishName.contains('羊') ||
        dishName.contains('羊肉') ||
        dishName.contains('羊排')) {
      ingredients.add('羊肉');
    }

    // 蛋类推断
    if (dishName.contains('蛋') ||
        dishName.contains('鸡蛋') ||
        dishName.contains('蛋花') ||
        dishName.contains('蛋白') ||
        dishName.contains('蛋黄')) {
      ingredients.add('鸡蛋');
    }

    // 豆制品推断
    if (dishName.contains('豆腐') ||
        dishName.contains('豆干') ||
        dishName.contains('豆皮')) {
      ingredients.add('豆腐');
    }
    if (dishName.contains('豆浆') ||
        dishName.contains('豆奶') ||
        dishName.contains('黄豆')) {
      ingredients.add('大豆');
    }

    // 坚果类推断
    if (dishName.contains('花生') ||
        dishName.contains('花生米') ||
        dishName.contains('花生仁')) {
      ingredients.add('花生');
    }
    if (dishName.contains('核桃') ||
        dishName.contains('核桃仁') ||
        dishName.contains('胡桃')) {
      ingredients.add('核桃');
    }
    if (dishName.contains('杏仁')) {
      ingredients.add('杏仁');
    }

    // 蔬菜类推断
    if (dishName.contains('白菜') ||
        dishName.contains('大白菜') ||
        dishName.contains('小白菜')) {
      ingredients.add('白菜');
    }
    if (dishName.contains('菠菜')) {
      ingredients.add('菠菜');
    }
    if (dishName.contains('韭菜') || dishName.contains('韭黄')) {
      ingredients.add('韭菜');
    }
    if (dishName.contains('萝卜') ||
        dishName.contains('白萝卜') ||
        dishName.contains('胡萝卜')) {
      ingredients.add('萝卜');
    }
    if (dishName.contains('土豆') ||
        dishName.contains('马铃薯') ||
        dishName.contains('薯条') ||
        dishName.contains('土豆丝')) {
      ingredients.add('土豆');
    }
    if (dishName.contains('茄子')) {
      ingredients.add('茄子');
    }
    if (dishName.contains('辣椒') ||
        dishName.contains('青椒') ||
        dishName.contains('红椒')) {
      ingredients.add('辣椒');
    }

    // 奶制品推断
    if (dishName.contains('奶') ||
        dishName.contains('牛奶') ||
        dishName.contains('乳') ||
        dishName.contains('奶制品')) {
      ingredients.add('牛奶');
    }
    if (dishName.contains('芝士') ||
        dishName.contains('奶酪') ||
        dishName.contains('乳酪') ||
        dishName.contains('起司')) {
      ingredients.add('奶酪');
    }

    // 调料类推断
    if (dishName.contains('芝麻') ||
        dishName.contains('麻油') ||
        dishName.contains('香油')) {
      ingredients.add('芝麻');
    }
    if (dishName.contains('味精') || dishName.contains('鸡精')) {
      ingredients.add('味精');
    }

    return ingredients.toSet().toList(); // 去重
  }

  // 根据配料获取过敏原
  static List<String> _getAllergensByIngredients(List<String> ingredients) {
    final allIngredients = DishDatabaseService.getAllIngredients();
    final allergens = <String>[];

    for (var ingredientName in ingredients) {
      final ingredient = allIngredients.firstWhere(
        (ing) => ing.name == ingredientName,
        orElse: () => Ingredient(
          id: '',
          name: '',
          category: '',
          allergens: [],
          keywords: [],
        ),
      );

      if (ingredient.id.isNotEmpty) {
        allergens.addAll(ingredient.allergens);
      }
    }

    return allergens.toSet().toList(); // 去重
  }

  // 找到匹配的过敏原（增强版 - 支持模糊匹配和同义词）
  static List<String> _findMatchedAllergens(
      List<String> detectedAllergens, List<String> userAllergens) {
    if (kDebugMode) {
      print('🔍 过敏原匹配调试:');
      print('  检测到的过敏原: $detectedAllergens');
      print('  用户设置的过敏原: $userAllergens');
    }

    final matched = <String>[];

    for (var detectedAllergen in detectedAllergens) {
      bool isMatched = false;
      String? matchedUserAllergen;

      for (var userAllergen in userAllergens) {
        // 1. 精确匹配（忽略大小写）
        if (userAllergen.toLowerCase() == detectedAllergen.toLowerCase()) {
          isMatched = true;
          matchedUserAllergen = userAllergen;
          break;
        }

        // 2. 包含匹配（双向）
        if (userAllergen.contains(detectedAllergen) ||
            detectedAllergen.contains(userAllergen)) {
          isMatched = true;
          matchedUserAllergen = userAllergen;
          break;
        }

        // 3. 同义词匹配
        if (_areAllergensSynonyms(detectedAllergen, userAllergen)) {
          isMatched = true;
          matchedUserAllergen = userAllergen;
          break;
        }

        // 4. 模糊匹配（相似度）
        final similarity =
            _calculateChineseSimilarity(detectedAllergen, userAllergen);
        if (similarity > 0.8) {
          isMatched = true;
          matchedUserAllergen = userAllergen;
          break;
        }
      }

      if (isMatched) {
        matched.add(detectedAllergen);
        if (kDebugMode) {
          print('  ✅ 匹配: "$detectedAllergen" <-> "$matchedUserAllergen"');
        }
      } else {
        if (kDebugMode) {
          print('  ❌ 未匹配: "$detectedAllergen"');
        }
      }
    }

    if (kDebugMode) {
      print('  最终匹配结果: $matched');
    }

    return matched;
  }

  // 检查两个过敏原是否为同义词
  static bool _areAllergensSynonyms(String allergen1, String allergen2) {
    final allergenSynonyms = {
      '虾': ['虾类', '虾仁', '明虾', '基围虾', '河虾', '海虾', '虾制品'],
      '蟹': ['蟹类', '螃蟹', '大闸蟹', '梭子蟹', '蟹肉', '蟹黄', '蟹制品'],
      '鱼': ['鱼类', '鲈鱼', '草鱼', '鲤鱼', '带鱼', '黄花鱼', '鱼片', '鱼肉', '鱼制品'],
      '花生': ['花生类', '花生米', '花生仁', '花生制品', '花生油'],
      '核桃': ['核桃类', '核桃仁', '胡桃', '核桃制品'],
      '杏仁': ['杏仁类', '杏仁仁', '杏仁制品'],
      '鸡蛋': ['蛋类', '蛋', '蛋液', '蛋白', '蛋黄', '蛋制品'],
      '牛奶': ['奶类', '乳制品', '奶', '乳', '牛乳', '鲜奶'],
      '奶酪': ['芝士', '乳酪', '起司', '奶酪制品'],
      '大豆': ['豆类', '黄豆', '豆制品', '豆腐', '豆浆'],
      '小麦': ['麦类', '面粉', '面制品', '小麦制品'],
      '芝麻': ['芝麻类', '芝麻油', '芝麻制品'],
      '味精': ['MSG', '谷氨酸钠', '鸡精'],
    };

    // 检查是否为直接同义词
    for (var entry in allergenSynonyms.entries) {
      final mainAllergen = entry.key;
      final synonyms = entry.value;

      if ((allergen1 == mainAllergen && synonyms.contains(allergen2)) ||
          (allergen2 == mainAllergen && synonyms.contains(allergen1)) ||
          (synonyms.contains(allergen1) && synonyms.contains(allergen2))) {
        return true;
      }
    }

    return false;
  }

  // 计算风险等级（增强版）
  static AllergenRiskLevel _calculateRiskLevel(
      List<String> matchedAllergens, List<String> userAllergens) {
    if (matchedAllergens.isEmpty) {
      return AllergenRiskLevel.safe;
    }

    if (kDebugMode) {
      print('🔍 开始风险等级计算:');
      print('  匹配的过敏原: $matchedAllergens');
    }

    // 定义过敏原严重程度等级
    final Map<String, int> allergenSeverity = {
      // 高风险过敏原 (严重程度: 3)
      '花生': 3, '虾': 3, '蟹': 3, '牛奶': 3, '鸡蛋': 3,
      // 中风险过敏原 (严重程度: 2)
      '鱼': 2, '大豆': 2, '小麦': 2, '核桃': 2, '杏仁': 2,
      // 低风险过敏原 (严重程度: 1)
      '芝麻': 1, '味精': 1, '奶酪': 1,
    };

    // 计算总风险分数
    int totalRiskScore = 0;
    int highSeverityCount = 0;
    int mediumSeverityCount = 0;

    for (var allergen in matchedAllergens) {
      final severity = allergenSeverity[allergen] ?? 1;
      totalRiskScore += severity;

      if (severity >= 3) {
        highSeverityCount++;
      } else if (severity >= 2) {
        mediumSeverityCount++;
      }

      if (kDebugMode) {
        print('  过敏原: $allergen, 严重程度: $severity');
      }
    }

    if (kDebugMode) {
      print('  总风险分数: $totalRiskScore');
      print('  高严重程度过敏原数量: $highSeverityCount');
      print('  中严重程度过敏原数量: $mediumSeverityCount');
    }

    // 基于综合评估确定风险等级
    AllergenRiskLevel riskLevel;

    if (highSeverityCount >= 2 || totalRiskScore >= 8) {
      // 多个高风险过敏原或总分很高
      riskLevel = AllergenRiskLevel.high;
    } else if (highSeverityCount >= 1 ||
        totalRiskScore >= 5 ||
        matchedAllergens.length >= 3) {
      // 有高风险过敏原或中等总分或过敏原数量多
      riskLevel = AllergenRiskLevel.medium;
    } else if (totalRiskScore >= 2 || matchedAllergens.length >= 2) {
      // 有一定风险
      riskLevel = AllergenRiskLevel.low;
    } else {
      // 风险较小
      riskLevel = AllergenRiskLevel.low;
    }

    if (kDebugMode) {
      print('  最终风险等级: ${riskLevel.displayName}');
    }

    return riskLevel;
  }

  // 生成警告信息
  static String _generateWarning(
      List<String> matchedAllergens, AllergenRiskLevel riskLevel) {
    if (matchedAllergens.isEmpty) {
      return '未检测到您设置的过敏原，相对安全';
    }

    final allergenList = matchedAllergens.join('、');

    switch (riskLevel) {
      case AllergenRiskLevel.low:
        return '检测到可能含有：$allergenList，请谨慎食用';
      case AllergenRiskLevel.medium:
        return '检测到含有：$allergenList，不建议食用';
      case AllergenRiskLevel.high:
        return '检测到多种过敏原：$allergenList，强烈不建议食用';
      default:
        return '检测到过敏原：$allergenList，请注意';
    }
  }

  // 生成建议
  static List<String> _generateSuggestions(
      List<String> matchedAllergens, List<String> ingredients) {
    final suggestions = <String>[];

    if (matchedAllergens.isEmpty) {
      suggestions.add('该菜品相对安全，可以食用');
      suggestions.add('如有疑虑，可询问服务员确认配料');
    } else {
      suggestions.add('避免食用此菜品');
      suggestions.add('询问服务员是否可以去除过敏原配料');
      suggestions.add('选择其他不含过敏原的菜品');

      // 根据具体过敏原给出建议
      if (matchedAllergens.contains('花生')) {
        suggestions.add('注意花生油也可能引起过敏反应');
      }
      if (matchedAllergens.contains('虾') || matchedAllergens.contains('蟹')) {
        suggestions.add('海鲜过敏者应避免所有海鲜类菜品');
      }
      if (matchedAllergens.contains('牛奶')) {
        suggestions.add('注意奶油、黄油等乳制品也需避免');
      }
    }

    return suggestions;
  }

  // 获取推荐的安全菜品
  static List<Dish> getRecommendedSafeDishes(List<String> userAllergens) {
    final allDishes = DishDatabaseService.getAllDishes();

    return allDishes
        .where((dish) {
          // 检查菜品是否不含用户过敏原
          return !dish.allergens.any((allergen) => userAllergens.any(
              (userAllergen) =>
                  userAllergen.toLowerCase() == allergen.toLowerCase()));
        })
        .take(5)
        .toList(); // 返回前5个安全菜品
  }

  // 批量检测多个菜品（增强版）
  static Future<List<AllergenDetectionResult>> batchDetectAllergens(
      List<String> dishNames) async {
    if (kDebugMode) {
      print('🍽️ 开始批量检测 ${dishNames.length} 道菜品');
    }

    final results = <AllergenDetectionResult>[];

    for (int i = 0; i < dishNames.length; i++) {
      final dishName = dishNames[i];
      if (kDebugMode) {
        print('  检测第 ${i + 1}/${dishNames.length} 道菜: $dishName');
      }

      final result = await detectAllergens(dishName);
      results.add(result);
    }

    // 生成批量检测摘要
    if (kDebugMode) {
      final safeCount =
          results.where((r) => r.riskLevel == AllergenRiskLevel.safe).length;
      final lowRiskCount =
          results.where((r) => r.riskLevel == AllergenRiskLevel.low).length;
      final mediumRiskCount =
          results.where((r) => r.riskLevel == AllergenRiskLevel.medium).length;
      final highRiskCount =
          results.where((r) => r.riskLevel == AllergenRiskLevel.high).length;
      final unknownCount =
          results.where((r) => r.riskLevel == AllergenRiskLevel.unknown).length;

      print('📊 批量检测摘要:');
      print('  安全菜品: $safeCount 道');
      print('  低风险菜品: $lowRiskCount 道');
      print('  中风险菜品: $mediumRiskCount 道');
      print('  高风险菜品: $highRiskCount 道');
      print('  未知风险菜品: $unknownCount 道');
    }

    return results;
  }

  /// 使用LLM增强的配料检测（新增）
  static Future<Map<String, dynamic>> detectIngredientsWithLLM(
    String dishName,
    String language,
  ) async {
    if (kDebugMode) {
      print('🤖 开始LLM增强配料检测: "$dishName" (语言: $language)');
    }

    try {
      // 1. 使用LLM分析配料
      final llmResult = await LLMIngredientService.analyzeIngredientsWithLLM(
          dishName, language);

      if (llmResult['success']) {
        if (kDebugMode) {
          print('✅ LLM分析成功: ${llmResult['source']}');
          print('📋 检测到配料: ${llmResult['ingredients']}');
        }

        // 2. 使用多语言过敏原检测
        final detectedAllergenTypes =
            MultilingualAllergenService.detectAllergensFromIngredients(
                List<String>.from(llmResult['ingredients']), language);

        // 3. 获取用户过敏原设置
        final userAllergens = AllergenService.getSelectedAllergensSync();
        final userAllergenNames = userAllergens.map((a) => a.name).toList();

        // 4. 匹配用户过敏原
        final matchedAllergens =
            MultilingualAllergenService.findMatchedAllergens(
                detectedAllergenTypes, userAllergenNames, 'zh' // 用户过敏原设置是中文
                );

        // 5. 生成警告信息
        final warning = MultilingualAllergenService.generateWarning(
            matchedAllergens, language);

        return {
          'success': true,
          'source': 'llm_enhanced',
          'dish_name': dishName,
          'language': language,
          'ingredients': llmResult['ingredients'],
          'detected_allergen_types': detectedAllergenTypes,
          'matched_allergens': matchedAllergens,
          'warning': warning,
          'confidence': llmResult['confidence'],
          'llm_source': llmResult['source'],
        };
      } else {
        if (kDebugMode) {
          print('❌ LLM分析失败，使用硬编码回退');
        }
        return _fallbackHardcodedDetection(dishName, language);
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ LLM配料检测异常: $e');
      }
      return _fallbackHardcodedDetection(dishName, language);
    }
  }

  /// 硬编码回退检测
  static Map<String, dynamic> _fallbackHardcodedDetection(
    String dishName,
    String language,
  ) {
    if (kDebugMode) {
      print('🔄 使用硬编码回退检测');
    }

    // 简单的硬编码配料推断
    final ingredients = <String>[];
    final dishLower = dishName.toLowerCase();

    // 基于菜名的配料推断
    if (language == 'zh') {
      if (dishLower.contains('鸡')) ingredients.addAll(['鸡肉', '盐', '油']);
      if (dishLower.contains('牛')) ingredients.addAll(['牛肉', '盐', '油']);
      if (dishLower.contains('猪')) ingredients.addAll(['猪肉', '盐', '油']);
      if (dishLower.contains('鱼')) ingredients.addAll(['鱼', '盐', '油']);
    } else if (language == 'en') {
      if (dishLower.contains('chicken'))
        ingredients.addAll(['chicken', 'salt', 'oil']);
      if (dishLower.contains('beef'))
        ingredients.addAll(['beef', 'salt', 'oil']);
      if (dishLower.contains('pork'))
        ingredients.addAll(['pork', 'salt', 'oil']);
      if (dishLower.contains('fish'))
        ingredients.addAll(['fish', 'salt', 'oil']);
    } else if (language == 'de') {
      if (dishLower.contains('hähnchen'))
        ingredients.addAll(['hähnchen', 'salz', 'öl']);
      if (dishLower.contains('rindfleisch'))
        ingredients.addAll(['rindfleisch', 'salz', 'öl']);
      if (dishLower.contains('schweinefleisch'))
        ingredients.addAll(['schweinefleisch', 'salz', 'öl']);
      if (dishLower.contains('fisch'))
        ingredients.addAll(['fisch', 'salz', 'öl']);
    }

    // 如果没有匹配，添加通用配料
    if (ingredients.isEmpty) {
      final commonIngredients = {
        'zh': ['盐', '油', '调料'],
        'en': ['salt', 'oil', 'seasoning'],
        'de': ['salz', 'öl', 'gewürze'],
      };
      ingredients
          .addAll(commonIngredients[language] ?? commonIngredients['en']!);
    }

    // 检测过敏原
    final detectedAllergenTypes =
        MultilingualAllergenService.detectAllergensFromIngredients(
            ingredients, language);

    return {
      'success': true,
      'source': 'hardcoded_fallback',
      'dish_name': dishName,
      'language': language,
      'ingredients': ingredients,
      'detected_allergen_types': detectedAllergenTypes,
      'matched_allergens': <String>[],
      'warning': '基础配料分析，建议人工确认',
      'confidence': 0.3,
      'llm_source': 'none',
    };
  }

  /// 从OCR文本中提取菜品信息（全面增强版）
  static Future<Map<String, dynamic>> extractDishesFromOcrTextEnhanced(
    List<String> ocrLines,
  ) async {
    if (kDebugMode) {
      print('🚀 开始全面OCR文本分析...');
      print('📝 OCR行数: ${ocrLines.length}');
    }

    // 1. 检测主要语言
    final String primaryLanguage = _detectPrimaryLanguage(ocrLines);
    if (kDebugMode) {
      print('🌍 主要语言: $primaryLanguage');
    }

    // 2. 结构化数据转换
    final Map<String, dynamic> structuredData =
        StructuredDataService.convertToStructuredData(
            ocrLines, primaryLanguage);

    // 3. 传统菜品匹配（保持兼容性）
    final List<Map<String, dynamic>> detectedDishes = [];
    final Set<String> processedTexts = {}; // 防止重复处理

    for (int i = 0; i < ocrLines.length; i++) {
      final String originalText = ocrLines[i];
      if (originalText.trim().isEmpty) continue;

      if (kDebugMode) {
        print('\n📍 处理第${i + 1}行: "$originalText"');
      }

      // 检测语言
      final String language = _detectLanguage(originalText);
      if (kDebugMode) {
        print('🌍 检测语言: $language');
      }

      // 根据语言清洗文本
      final String cleanedText =
          _cleanOcrTextByLanguage(originalText, language);

      // 防止重复处理相同的文本
      if (processedTexts.contains(cleanedText)) {
        if (kDebugMode) {
          print('⏭️ 跳过重复文本: "$cleanedText"');
        }
        continue;
      }
      processedTexts.add(cleanedText);

      // 根据语言判断是否为菜品
      if (!_isLikelyDishNameByLanguage(cleanedText, language)) {
        continue;
      }

      if (kDebugMode) {
        print('🎯 尝试匹配菜品: "$cleanedText"');
      }

      // 在数据库中搜索匹配的菜品
      final List<Dish> matchedDishes =
          DishDatabaseService.searchDishes(cleanedText);

      if (matchedDishes.isNotEmpty) {
        if (kDebugMode) {
          print('✅ 找到 ${matchedDishes.length} 个匹配菜品');
        }

        for (final dish in matchedDishes) {
          final Map<String, dynamic> dishInfo = {
            'original_text': originalText,
            'cleaned_text': cleanedText,
            'detected_language': language,
            'line_number': i + 1,
            'dish': dish,
            'confidence': _calculateMatchConfidence(cleanedText, dish.name),
            'processing_timestamp': DateTime.now().toIso8601String(),
          };

          detectedDishes.add(dishInfo);

          if (kDebugMode) {
            print('📋 添加菜品: ${dish.name} (置信度: ${dishInfo['confidence']})');
          }
        }
      } else {
        if (kDebugMode) {
          print('❌ 未找到匹配菜品');
        }
      }
    }

    // 4. 合并结果
    final Map<String, dynamic> enhancedResult = {
      'traditional_dishes': detectedDishes,
      'structured_data': structuredData,
      'summary': {
        'total_dishes_found': detectedDishes.length,
        'primary_language': primaryLanguage,
        'processing_timestamp': DateTime.now().toIso8601String(),
        'enhanced_features': {
          'layout_analysis': true,
          'entity_recognition': true,
          'allergen_detection': true,
          'price_extraction': true,
          'section_identification': true,
        }
      }
    };

    if (kDebugMode) {
      print('\n🎉 全面OCR分析完成！');
      print('📊 传统匹配: ${detectedDishes.length} 个菜品');
      print('📊 结构化识别: ${structuredData['dishes'].length} 个实体');
      print('📊 过敏原检测: ${structuredData['allergens'].length} 种');
      print('📊 价格信息: ${structuredData['prices'].length} 项');
    }

    return enhancedResult;
  }

  // 智能菜品提取（从OCR文本中提取多个菜品名称）- 多语言增强版
  static List<String> extractDishNames(List<String> ocrLines) {
    final dishNames = <String>[];

    // 强制输出调试信息（Web版本）
    if (kDebugMode) {
      print('🔍 开始智能菜品提取，共 ${ocrLines.length} 行文本');
      print('📝 原始OCR文本:');
      for (int i = 0; i < ocrLines.length; i++) {
        print(
            '  第${i + 1}行: "${ocrLines[i]}" (字节长度: ${ocrLines[i].codeUnits.length})');
      }
    }

    for (int i = 0; i < ocrLines.length; i++) {
      final line = ocrLines[i];

      // 检测语言并使用相应的清洗方法
      final language = _detectLanguage(line);
      final cleanLine = _cleanOcrTextByLanguage(line, language);

      if (kDebugMode) {
        print(
            '  检查第${i + 1}行: "$line" -> "$cleanLine" (长度: ${cleanLine.length}, 语言: $language)');
      }

      // 放宽长度限制
      if (cleanLine.length < 2 || cleanLine.length > 20) {
        if (kDebugMode) {
          print('    ❌ 长度不符合要求');
        }
        continue;
      }

      // 过滤掉明显不是菜品的文本
      if (_isLikelyDishNameByLanguage(cleanLine, language)) {
        dishNames.add(cleanLine);
        if (kDebugMode) {
          print('  ✅ 提取菜品: $cleanLine (语言: $language)');

          // 立即测试数据库匹配
          final testMatches = DishDatabaseService.searchDishes(cleanLine);
          if (testMatches.isNotEmpty) {
            print('    ✅ 找到匹配: ${testMatches.map((d) => d.name).toList()}');
          } else {
            print('    ❌ 未找到匹配');
          }
        }
      } else {
        if (kDebugMode) {
          print('    ❌ 不像菜品名称');
        }
      }
    }

    // 去重但不限制数量（提取所有识别到的菜品）
    final uniqueDishNames = dishNames.toSet().toList();

    if (kDebugMode) {
      print('🎯 最终提取的菜品: $uniqueDishNames');
    }

    return uniqueDishNames;
  }

  // 清洗OCR识别的文本（修复版本 - 保留菜品名称中的重要字符）
  static String _cleanOcrText(String text) {
    String cleaned = text;

    print('    🧹 文字清洗: "$text"');

    // 1. 去除首尾空格
    cleaned = cleaned.trim();

    // 2. 去除多余空格，但保留单个空格
    cleaned = cleaned.replaceAll(RegExp(r'\s+'), ' ');

    // 3. 去除常见的中文标点符号
    cleaned =
        cleaned.replaceAll(RegExp(r'[，。、；：！？""' '（）【】《》〈〉「」『』〔〕［］｛｝]'), '');

    // 4. 去除英文标点符号
    cleaned = cleaned.replaceAll(RegExp(r'[,.;:!?"\(\)\[\]{}]'), '');

    // 5. 只去除价格相关符号，保留菜品名称中可能的数字
    cleaned = cleaned.replaceAll(RegExp(r'[￥¥元兀]'), '');

    // 去除明显的价格数字（如 "18.5" "25" 等独立的数字）
    // 但保留菜品名称中的数字（如"三杯鸡"中的"三"）
    cleaned = cleaned.replaceAll(RegExp(r'\b\d+\.?\d*\b'), '');

    // 6. 去除其他特殊符号
    cleaned = cleaned.replaceAll(RegExp(r'[-_=+|\\\/\*&%#@\$\^~`]'), '');

    // 7. 处理常见的OCR识别错误（大幅扩展版）
    final ocrCorrections = {
      // 基础字符纠错
      '兀': '元', '兄': '元', '无': '元',
      '鸡': '鸡', '机': '鸡', '几': '鸡',
      '保': '保', '堡': '保', '宝': '保',
      '宫': '宫', '官': '宫', '公': '宫',
      '丁': '丁', '了': '丁', '亍': '丁',
      '麻': '麻', '马': '麻', '妈': '麻',
      '婆': '婆', '波': '婆', '破': '婆',
      '豆': '豆', '斗': '豆', '逗': '豆',
      '腐': '腐', '府': '腐', '付': '腐',
      '鱼': '鱼', '渔': '鱼', '余': '鱼',
      '虾': '虾', '瞎': '虾', '侠': '虾',
      '蟹': '蟹', '解': '蟹', '谢': '蟹',

      // 蔬菜类纠错
      '菜': '菜', '采': '菜', '彩': '菜',
      '白': '白', '百': '白', '柏': '白',
      '青': '青', '清': '青', '情': '青',
      '红': '红', '洪': '红', '宏': '红',
      '绿': '绿', '录': '绿', '禄': '绿',
      '黄': '黄', '皇': '黄', '煌': '黄',

      // 肉类纠错
      '牛': '牛', '午': '牛', '生': '牛',
      '猪': '猪', '诸': '猪', '朱': '猪',
      '羊': '羊', '洋': '羊', '样': '羊',
      '肉': '肉', '内': '肉', '入': '肉',

      // 调料纠错
      '盐': '盐', '严': '盐', '岩': '盐',
      '糖': '糖', '塘': '糖', '堂': '糖',
      '醋': '醋', '促': '醋', '族': '醋',
      '油': '油', '由': '油', '邮': '油',
      '酱': '酱', '将': '酱', '浆': '酱',

      // 烹饪方法纠错
      '炒': '炒', '抄': '炒', '钞': '炒',
      '煮': '煮', '者': '煮', '著': '煮',
      '蒸': '蒸', '征': '蒸', '正': '蒸',
      '烧': '烧', '少': '烧', '绍': '烧',
      '炖': '炖', '顿': '炖', '盾': '炖',
      '焖': '焖', '门': '焖', '闷': '焖',
      '炸': '炸', '作': '炸', '乍': '炸',
      '烤': '烤', '考': '烤', '靠': '烤',

      // 容器/形状纠错
      '丝': '丝', '司': '丝', '思': '丝',
      '片': '片', '篇': '片', '偏': '片',
      '块': '块', '快': '块', '筷': '块',
      '条': '条', '跳': '条', '调': '条',
      '段': '段', '断': '段', '锻': '段',

      // 数字纠错
      '一': '一', '1': '一', 'l': '一',
      '二': '二', '2': '二', '贰': '二',
      '三': '三', '3': '三', '叁': '三',
      '四': '四', '4': '四', '肆': '四',
      '五': '五', '5': '五', '伍': '五',
      '六': '六', '6': '六', '陆': '六',
      '七': '七', '7': '七', '柒': '七',
      '八': '八', '8': '八', '捌': '八',
      '九': '九', '9': '九', '玖': '九',
      '十': '十', '0': '十', 'o': '十',
    };

    ocrCorrections.forEach((wrong, correct) {
      cleaned = cleaned.replaceAll(wrong, correct);
    });

    // 8. 最终去除多余空格
    cleaned = cleaned.trim().replaceAll(RegExp(r'\s+'), '');

    print('    🧹 清洗结果: "$cleaned"');
    return cleaned;
  }

  // 使用与数据库一致的清洗方式（用于匹配测试）
  static String _cleanForDatabaseSearch(String text) {
    String cleaned = text.toLowerCase().trim();

    // 去除空格
    cleaned = cleaned.replaceAll(' ', '');

    // 去除标点符号
    cleaned =
        cleaned.replaceAll(RegExp(r'[，。、；：！？""' '（）【】《》〈〉「」『』〔〕［］｛｝]'), '');
    cleaned = cleaned.replaceAll(RegExp(r'[,.;:!?"\(\)\[\]{}]'), '');

    // 去除数字和价格符号
    cleaned = cleaned.replaceAll(RegExp(r'[0-9￥¥元兀]'), '');

    return cleaned;
  }

  // 判断文本是否像菜品名称（超级宽松版本）
  static bool _isLikelyDishName(String text) {
    print('    🔍 检查是否为菜品: "$text"');

    // 只过滤掉最明显的非菜品内容
    if (RegExp(r'^\d+\.?\d*$').hasMatch(text)) {
      print('      ❌ 纯数字');
      return false;
    }

    // 过滤掉明显的价格行
    if (RegExp(r'^\d+\.?\d*元$').hasMatch(text)) {
      print('      ❌ 价格格式');
      return false;
    }

    // 超级宽松的长度限制
    if (text.length < 2) {
      print('      ❌ 太短 (${text.length})');
      return false;
    }

    // 过滤掉明显的非菜品词汇
    final nonDishKeywords = [
      '菜单',
      '价格',
      '荤菜',
      '素菜',
      '家常菜',
      '特色菜',
      '招牌菜',
      '热菜',
      '凉菜',
      '主食',
      '汤类',
      '饮品',
      '酒水',
      '小食',
      '元',
      '￥',
      '¥',
      '价',
      '费',
      '收费',
      '免费'
    ];

    bool hasNonDishKeyword =
        nonDishKeywords.any((keyword) => text.contains(keyword));
    if (hasNonDishKeyword) {
      print('      ❌ 包含非菜品关键词');
      return false;
    }

    // 包含中文字符就认为可能是菜品（超级宽松）
    bool hasChinese = RegExp(r'[\u4e00-\u9fa5]').hasMatch(text);

    if (hasChinese) {
      print('      ✅ 包含中文，认为是菜品候选');
      return true;
    }

    // 如果没有中文，检查是否有菜品相关的英文或其他字符
    if (text.length >= 3) {
      print('      ✅ 长度足够，认为是菜品候选');
      return true;
    }

    print('      ❌ 不像菜品');
    return false;
  }

  // === 多语言支持函数 ===

  // 检测文本语言
  static String _detectLanguage(String text) {
    if (text.isEmpty) return 'zh'; // 默认中文

    int chineseChars = 0;
    int englishChars = 0;
    int germanChars = 0;

    for (int i = 0; i < text.length; i++) {
      final char = text[i];
      final codeUnit = char.codeUnitAt(0);

      if ((codeUnit >= 0x4E00 && codeUnit <= 0x9FFF) ||
          (codeUnit >= 0x3400 && codeUnit <= 0x4DBF)) {
        chineseChars++;
      } else if ((codeUnit >= 65 && codeUnit <= 90) ||
          (codeUnit >= 97 && codeUnit <= 122)) {
        englishChars++;
      } else if (char == 'ä' ||
          char == 'ö' ||
          char == 'ü' ||
          char == 'Ä' ||
          char == 'Ö' ||
          char == 'Ü' ||
          char == 'ß') {
        germanChars++;
      }
    }

    if (chineseChars > 0) {
      return 'zh';
    } else if (germanChars > 0) {
      return 'de';
    } else if (englishChars > 0) {
      return 'en';
    } else {
      return 'zh'; // 默认中文
    }
  }

  // 根据语言清洗OCR文本
  static String _cleanOcrTextByLanguage(String text, String language) {
    switch (language) {
      case 'zh':
        return _cleanOcrText(text); // 使用原有的中文清洗
      case 'en':
        return _cleanEnglishOcrText(text);
      case 'de':
        return _cleanGermanOcrText(text);
      default:
        return _cleanOcrText(text);
    }
  }

  // 英文OCR文本清洗
  static String _cleanEnglishOcrText(String text) {
    var cleaned = text.trim().toLowerCase();

    if (kDebugMode) {
      print('    🧹 英文清洗: "$text"');
    }

    // 保留空格，但规范化
    cleaned = cleaned.replaceAll(RegExp(r'\s+'), ' ');

    // 去除标点符号
    cleaned = cleaned.replaceAll(RegExp(r'[,.;:!?"\(\)\[\]{}]'), '');

    // 去除价格符号
    cleaned = cleaned.replaceAll(RegExp(r'[\$£€]'), '');
    cleaned = cleaned.replaceAll(RegExp(r'\b\d+\.?\d*\b'), '');

    // 去除常见噪音词（扩展版）
    final noiseWords = [
      'menu',
      'special',
      'today',
      'fresh',
      'new',
      'hot',
      'cold',
      'daily',
      'chef',
      'house',
      'signature',
      'premium',
      'deluxe',
      'served',
      'with',
      'choice',
      'includes',
      'comes',
      'available'
    ];
    for (final noise in noiseWords) {
      cleaned = cleaned.replaceAll(RegExp(r'\b' + noise + r'\b'), '');
    }

    // 英文OCR纠错（大幅扩展）
    final englishCorrections = {
      // 肉类纠错
      'chickcn': 'chicken', 'chiken': 'chicken', 'chickn': 'chicken',
      'chlcken': 'chicken', 'cnicken': 'chicken', 'chickcm': 'chicken',
      'beaf': 'beef', 'beff': 'beef', 'becf': 'beef',
      'prok': 'pork', 'prk': 'pork', 'porc': 'pork',
      'fisb': 'fish', 'fiah': 'fish', 'flsh': 'fish',
      'lamh': 'lamb', 'lanb': 'lamb', 'iamb': 'lamb',

      // 食物类型纠错
      'burgcr': 'burger', 'hurger': 'burger', 'burgcr': 'burger',
      'pizze': 'pizza', 'plzza': 'pizza', 'pjzza': 'pizza',
      'salad': 'salad', 'selad': 'salad', 'saled': 'salad',
      'sonp': 'soup', 'souo': 'soup', 'soun': 'soup',
      'stcak': 'steak', 'stcek': 'steak', 'steac': 'steak',
      'sandwlch': 'sandwich', 'sandwicn': 'sandwich', 'sandwjch': 'sandwich',

      // 配菜纠错
      'frics': 'fries', 'frles': 'fries', 'frjes': 'fries',
      'ricc': 'rice', 'rlce': 'rice', 'rjce': 'rice',
      'pasta': 'pasta', 'pesta': 'pasta', 'pasfa': 'pasta',
      'bread': 'bread', 'brcad': 'bread', 'breac': 'bread',

      // 蔬菜纠错
      'lettuce': 'lettuce', 'lcttuce': 'lettuce', 'lettice': 'lettuce',
      'tomato': 'tomato', 'tometo': 'tomato', 'tomatc': 'tomato',
      'onlon': 'onion', 'onjon': 'onion', 'onlcn': 'onion',
      'mushroom': 'mushroom', 'mushroorn': 'mushroom', 'mushrcom': 'mushroom',

      // 调料纠错
      'chcese': 'cheese', 'cheesc': 'cheese', 'cnecse': 'cheese',
      'butter': 'butter', 'buttcr': 'butter', 'butfer': 'butter',
      'cream': 'cream', 'crcam': 'cream', 'crearn': 'cream',
      'sauce': 'sauce', 'saucc': 'sauce', 'saucr': 'sauce',
    };

    englishCorrections.forEach((wrong, correct) {
      cleaned = cleaned.replaceAll(wrong, correct);
    });

    cleaned = cleaned.trim().replaceAll(RegExp(r'\s+'), ' ');

    if (kDebugMode) {
      print('    🧹 英文清洗结果: "$cleaned"');
    }

    return cleaned;
  }

  // 德文OCR文本清洗（增强版）
  static String _cleanGermanOcrText(String text) {
    var cleaned = text.trim().toLowerCase();

    if (kDebugMode) {
      print('    🧹 德文清洗: "$text"');
    }

    cleaned = cleaned.replaceAll(RegExp(r'\s+'), ' ');
    cleaned = cleaned.replaceAll(RegExp(r'[,.;:!?"\(\)\[\]{}]'), '');
    cleaned = cleaned.replaceAll(RegExp(r'[€]'), '');
    cleaned = cleaned.replaceAll(RegExp(r'\b\d+[,.]?\d*\b'), '');

    // 德语噪音词（扩展版）
    final noiseWords = [
      'speisekarte',
      'spezial',
      'heute',
      'frisch',
      'neu',
      'heiß',
      'täglich',
      'küchenchef',
      'haus',
      'signatur',
      'premium',
      'serviert',
      'mit',
      'wahl',
      'enthält',
      'kommt',
      'verfügbar'
    ];
    for (final noise in noiseWords) {
      cleaned = cleaned.replaceAll(RegExp(r'\b' + noise + r'\b'), '');
    }

    // 德文OCR纠错（大幅扩展）
    final germanCorrections = {
      // 肉类纠错
      'hähnchen': 'hähnchen', 'hahnchen': 'hähnchen', 'haehnchen': 'hähnchen',
      'rindfleisch': 'rindfleisch', 'rindflcisch': 'rindfleisch',
      'rindfleiscn': 'rindfleisch',
      'schweinefleisch': 'schweinefleisch',
      'schweinefleiscn': 'schweinefleisch',
      'lamm': 'lamm', 'lanm': 'lamm', 'iamm': 'lamm',

      // 德式菜品纠错
      'schnitzel': 'schnitzel', 'scnnitzel': 'schnitzel',
      'schnitzcl': 'schnitzel',
      'bratwurst': 'bratwurst', 'bratwursl': 'bratwurst',
      'bratwurct': 'bratwurst',
      'sauerbraten': 'sauerbraten', 'sauerbraten': 'sauerbraten',
      'currywurst': 'currywurst', 'currywursl': 'currywurst',

      // 汤类纠错
      'suppe': 'suppe', 'auppe': 'suppe', 'suppc': 'suppe',
      'eintopf': 'eintopf', 'eintoof': 'eintopf', 'eintopf': 'eintopf',

      // 蔬菜纠错
      'salat': 'salat', 'salet': 'salat', 'salaf': 'salat',
      'kartoffel': 'kartoffel', 'kartoffcl': 'kartoffel',
      'kartoffel': 'kartoffel',
      'zwiebel': 'zwiebel', 'zwiebcl': 'zwiebel', 'zwicbel': 'zwiebel',

      // 调料纠错
      'käse': 'käse', 'kase': 'käse', 'kaese': 'käse',
      'butter': 'butter', 'buttcr': 'butter', 'butfer': 'butter',
      'sahne': 'sahne', 'sahnc': 'sahne', 'sahne': 'sahne',
      'soße': 'soße', 'sosse': 'soße', 'sosse': 'soße',

      // 面包类纠错
      'brot': 'brot', 'brof': 'brot', 'brct': 'brot',
      'brötchen': 'brötchen', 'broetchen': 'brötchen', 'brötcnen': 'brötchen',
    };

    germanCorrections.forEach((wrong, correct) {
      cleaned = cleaned.replaceAll(wrong, correct);
    });

    cleaned = cleaned.trim().replaceAll(RegExp(r'\s+'), ' ');

    if (kDebugMode) {
      print('    🧹 德文清洗结果: "$cleaned"');
    }

    return cleaned;
  }

  // 根据语言判断是否为菜品名称
  static bool _isLikelyDishNameByLanguage(String text, String language) {
    switch (language) {
      case 'zh':
        return _isLikelyDishName(text); // 使用原有的中文判断
      case 'en':
        return _isLikelyEnglishDish(text);
      case 'de':
        return _isLikelyGermanDish(text);
      default:
        return _isLikelyDishName(text);
    }
  }

  // 判断是否为英文菜品
  static bool _isLikelyEnglishDish(String text) {
    if (kDebugMode) {
      print('    🔍 检查英文菜品: "$text"');
    }

    // 过滤纯数字
    if (RegExp(r'^\d+\.?\d*$').hasMatch(text)) {
      if (kDebugMode) {
        print('      ❌ 纯数字');
      }
      return false;
    }

    // 长度检查
    if (text.length < 3) {
      if (kDebugMode) {
        print('      ❌ 太短');
      }
      return false;
    }

    // 英文菜品关键词（大幅扩展）
    final dishKeywords = [
      // 肉类
      'chicken', 'beef', 'pork', 'fish', 'lamb', 'turkey', 'duck', 'salmon',
      'tuna',
      'shrimp', 'lobster', 'crab', 'bacon', 'ham', 'sausage',

      // 主食类型
      'burger', 'pizza', 'sandwich', 'wrap', 'pasta', 'noodles', 'rice',
      'risotto',
      'steak', 'chop', 'roast', 'grilled', 'fried', 'baked', 'steamed',

      // 汤和沙拉
      'soup', 'salad', 'chowder', 'bisque', 'broth', 'stew',

      // 配菜
      'fries', 'mashed', 'roasted', 'sauteed', 'vegetables', 'potatoes',

      // 甜品
      'cake', 'pie', 'ice cream', 'pudding', 'mousse', 'tart',

      // 早餐
      'eggs', 'pancakes', 'waffles', 'toast', 'cereal', 'omelette',

      // 饮品
      'coffee', 'tea', 'juice', 'smoothie', 'latte', 'cappuccino'
    ];
    bool hasKeyword = dishKeywords.any((keyword) => text.contains(keyword));

    if (hasKeyword) {
      if (kDebugMode) {
        print('      ✅ 包含英文菜品关键词');
      }
      return true;
    }

    // 如果长度足够，也认为可能是菜品
    if (text.length >= 4 && text.contains(' ')) {
      if (kDebugMode) {
        print('      ✅ 长度足够且包含空格');
      }
      return true;
    }

    if (kDebugMode) {
      print('      ❌ 不像英文菜品');
    }
    return false;
  }

  // 判断是否为德文菜品
  static bool _isLikelyGermanDish(String text) {
    if (kDebugMode) {
      print('    🔍 检查德文菜品: "$text"');
    }

    // 过滤纯数字
    if (RegExp(r'^\d+[,.]?\d*$').hasMatch(text)) {
      if (kDebugMode) {
        print('      ❌ 纯数字');
      }
      return false;
    }

    // 长度检查
    if (text.length < 3) {
      if (kDebugMode) {
        print('      ❌ 太短');
      }
      return false;
    }

    // 德文菜品关键词（大幅扩展）
    final dishKeywords = [
      // 肉类
      'hähnchen', 'rindfleisch', 'schweinefleisch', 'fisch', 'lamm', 'pute',
      'ente', 'lachs', 'thunfisch', 'garnelen', 'hummer', 'krabbe',
      'speck', 'schinken', 'wurst',

      // 德式特色菜
      'schnitzel', 'bratwurst', 'currywurst', 'sauerbraten', 'rouladen',
      'eisbein', 'kassler', 'leberwurst', 'blutwurst', 'weißwurst',

      // 汤和沙拉
      'suppe', 'salat', 'eintopf', 'brühe', 'gulasch',

      // 配菜
      'kartoffel', 'pommes', 'sauerkraut', 'rotkohl', 'spätzle',
      'knödel', 'semmelknödel', 'gemüse',

      // 面包和烘焙
      'brot', 'brötchen', 'brezel', 'kuchen', 'torte', 'strudel',

      // 饮品
      'kaffee', 'tee', 'saft', 'bier', 'wein', 'schnaps'
    ];
    bool hasKeyword = dishKeywords.any((keyword) => text.contains(keyword));

    if (hasKeyword) {
      if (kDebugMode) {
        print('      ✅ 包含德文菜品关键词');
      }
      return true;
    }

    // 如果长度足够，也认为可能是菜品
    if (text.length >= 4) {
      if (kDebugMode) {
        print('      ✅ 长度足够');
      }
      return true;
    }

    if (kDebugMode) {
      print('      ❌ 不像德文菜品');
    }
    return false;
  }

  // === 辅助函数 ===

  // 检测主要语言（从多行文本中）
  static String _detectPrimaryLanguage(List<String> lines) {
    final Map<String, int> languageCounts = {'zh': 0, 'en': 0, 'de': 0};

    for (String line in lines) {
      if (line.trim().isEmpty) continue;
      final language = _detectLanguage(line);
      languageCounts[language] = (languageCounts[language] ?? 0) + 1;
    }

    // 返回出现次数最多的语言
    String primaryLanguage = 'zh';
    int maxCount = 0;
    languageCounts.forEach((lang, count) {
      if (count > maxCount) {
        maxCount = count;
        primaryLanguage = lang;
      }
    });

    return primaryLanguage;
  }

  // 计算匹配置信度
  static double _calculateMatchConfidence(String query, String dishName) {
    if (query.isEmpty || dishName.isEmpty) return 0.0;

    final cleanQuery = query.toLowerCase().trim();
    final cleanDishName = dishName.toLowerCase().trim();

    // 完全匹配
    if (cleanQuery == cleanDishName) return 1.0;

    // 包含匹配
    if (cleanDishName.contains(cleanQuery)) return 0.8;
    if (cleanQuery.contains(cleanDishName)) return 0.7;

    // 字符相似度
    final similarity = _calculateStringSimilarity(cleanQuery, cleanDishName);
    return similarity * 0.6;
  }

  // 计算字符串相似度
  static double _calculateStringSimilarity(String s1, String s2) {
    if (s1.isEmpty || s2.isEmpty) return 0.0;

    final longer = s1.length > s2.length ? s1 : s2;
    final shorter = s1.length > s2.length ? s2 : s1;

    if (longer.length == 0) return 1.0;

    final editDistance = _calculateEditDistance(longer, shorter);
    return (longer.length - editDistance) / longer.length;
  }

  // 计算编辑距离
  static int _calculateEditDistance(String s1, String s2) {
    final matrix =
        List.generate(s1.length + 1, (i) => List.filled(s2.length + 1, 0));

    for (int i = 0; i <= s1.length; i++) {
      matrix[i][0] = i;
    }

    for (int j = 0; j <= s2.length; j++) {
      matrix[0][j] = j;
    }

    for (int i = 1; i <= s1.length; i++) {
      for (int j = 1; j <= s2.length; j++) {
        final cost = s1[i - 1] == s2[j - 1] ? 0 : 1;
        matrix[i][j] = [
          matrix[i - 1][j] + 1, // deletion
          matrix[i][j - 1] + 1, // insertion
          matrix[i - 1][j - 1] + cost // substitution
        ].reduce((a, b) => a < b ? a : b);
      }
    }

    return matrix[s1.length][s2.length];
  }
}
