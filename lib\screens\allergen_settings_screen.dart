import 'package:flutter/material.dart';
import '../models/allergen.dart';
import '../services/allergen_service.dart';

class AllergenSettingsScreen extends StatefulWidget {
  const AllergenSettingsScreen({super.key});

  @override
  State<AllergenSettingsScreen> createState() => _AllergenSettingsScreenState();
}

class _AllergenSettingsScreenState extends State<AllergenSettingsScreen> {
  List<Allergen> _allergens = [];
  final TextEditingController _customNameController = TextEditingController();
  String _selectedCategory = '其他';
  bool _isLoading = true;

  final List<String> _categories = [
    '海鲜类',
    '坚果类',
    '乳制品',
    '蛋类',
    '豆类',
    '谷物类',
    '其他',
  ];

  @override
  void initState() {
    super.initState();
    _loadAllergens();
  }

  @override
  void dispose() {
    _customNameController.dispose();
    super.dispose();
  }

  Future<void> _loadAllergens() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final allergens = await AllergenService.getAllAllergens();
      setState(() {
        _allergens = allergens;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      _showErrorSnackBar('加载过敏原设置失败: $e');
    }
  }

  Future<void> _toggleAllergen(String allergenId, bool isSelected) async {
    try {
      await AllergenService.updateAllergenSelection(allergenId, isSelected);
      await _loadAllergens();

      final allergen = _allergens.firstWhere((a) => a.id == allergenId);
      _showSuccessSnackBar(isSelected
          ? '已添加 ${allergen.name} 到过敏原列表'
          : '已从过敏原列表移除 ${allergen.name}');
    } catch (e) {
      _showErrorSnackBar('更新过敏原设置失败: $e');
    }
  }

  Future<void> _addCustomAllergen() async {
    final name = _customNameController.text.trim();
    if (name.isEmpty) {
      _showErrorSnackBar('请输入过敏原名称');
      return;
    }

    try {
      await AllergenService.addCustomAllergen(name, _selectedCategory);
      await _loadAllergens();
      _customNameController.clear();
      if (mounted) {
        Navigator.of(context).pop();
        _showSuccessSnackBar('已添加自定义过敏原: $name');
      }
    } catch (e) {
      if (mounted) {
        _showErrorSnackBar('添加自定义过敏原失败: $e');
      }
    }
  }

  Future<void> _removeCustomAllergen(String allergenId) async {
    try {
      await AllergenService.removeCustomAllergen(allergenId);
      await _loadAllergens();
      _showSuccessSnackBar('已删除自定义过敏原');
    } catch (e) {
      _showErrorSnackBar('删除自定义过敏原失败: $e');
    }
  }

  void _showAddCustomAllergenDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('添加自定义过敏原'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextField(
              controller: _customNameController,
              decoration: const InputDecoration(
                labelText: '过敏原名称',
                hintText: '例如：芒果、辣椒等',
              ),
            ),
            const SizedBox(height: 16),
            DropdownButtonFormField<String>(
              value: _selectedCategory,
              decoration: const InputDecoration(
                labelText: '分类',
              ),
              items: _categories.map((category) {
                return DropdownMenuItem(
                  value: category,
                  child: Text(category),
                );
              }).toList(),
              onChanged: (value) {
                setState(() {
                  _selectedCategory = value!;
                });
              },
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('取消'),
          ),
          ElevatedButton(
            onPressed: _addCustomAllergen,
            child: const Text('添加'),
          ),
        ],
      ),
    );
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
      ),
    );
  }

  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.green,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('过敏原设置'),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
        actions: [
          IconButton(
            onPressed: _showAddCustomAllergenDialog,
            icon: const Icon(Icons.add),
            tooltip: '添加自定义过敏原',
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _buildAllergenList(),
    );
  }

  Widget _buildAllergenList() {
    if (_allergens.isEmpty) {
      return const Center(
        child: Text('暂无过敏原数据'),
      );
    }

    // 按分类分组
    final Map<String, List<Allergen>> groupedAllergens = {};
    for (var allergen in _allergens) {
      groupedAllergens.putIfAbsent(allergen.category, () => []).add(allergen);
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: groupedAllergens.length,
      itemBuilder: (context, index) {
        final category = groupedAllergens.keys.elementAt(index);
        final allergens = groupedAllergens[category]!;

        return Card(
          margin: const EdgeInsets.only(bottom: 16),
          child: ExpansionTile(
            title: Text(
              category,
              style: const TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: 16,
              ),
            ),
            subtitle: Text('${allergens.length}种过敏原'),
            children: allergens.map((allergen) {
              return ListTile(
                title: Text(allergen.name),
                subtitle: Text(allergen.description),
                trailing: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Switch(
                      value: allergen.isSelected,
                      onChanged: (value) => _toggleAllergen(allergen.id, value),
                    ),
                    if (!allergen.isCommon)
                      IconButton(
                        onPressed: () => _removeCustomAllergen(allergen.id),
                        icon: const Icon(Icons.delete, color: Colors.red),
                        tooltip: '删除自定义过敏原',
                      ),
                  ],
                ),
              );
            }).toList(),
          ),
        );
      },
    );
  }
}
