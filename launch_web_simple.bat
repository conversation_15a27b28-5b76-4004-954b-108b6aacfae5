@echo off
echo ========================================
echo Azure OCR Web版启动器
echo ========================================
echo.

REM 检查.env文件
if not exist ".env" (
    echo [错误] .env文件不存在
    echo.
    echo 请先创建.env文件:
    echo 1. copy .env.example .env
    echo 2. 编辑.env文件，添加您的Azure配置
    echo.
    pause
    exit /b 1
)

echo [✓] .env文件存在
echo.

REM 显示当前配置
echo 当前配置检查:
echo - Flutter Web支持: 已启用
echo - 项目目录: %CD%
echo - 启动方式: Chrome浏览器
echo.

echo [信息] 正在启动Web应用...
echo [信息] 首次启动可能需要1-2分钟进行编译
echo [信息] 请耐心等待Chrome浏览器自动打开
echo.
echo [提示] 如果长时间无响应，请按Ctrl+C停止并检查错误
echo.

REM 启动Flutter Web应用
flutter run -d chrome --verbose

echo.
echo [完成] 应用已启动
pause
