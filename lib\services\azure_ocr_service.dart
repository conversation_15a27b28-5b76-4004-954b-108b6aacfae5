import 'dart:convert';
import 'dart:io';
import 'dart:typed_data';
import 'package:http/http.dart' as http;
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:image_picker/image_picker.dart';

class AzureOcrService {
  static final String _endpoint = dotenv.env['AZURE_VISION_ENDPOINT'] ?? '';
  static final String _apiKey = dotenv.env['AZURE_VISION_KEY'] ?? '';

  static Future<String> extractTextFromImage(File imageFile) async {
    if (_endpoint.isEmpty || _apiKey.isEmpty) {
      throw Exception('Azure配置信息缺失，请检查.env文件');
    }

    try {
      final bytes = await imageFile.readAsBytes();
      return await _performOcr(bytes);
    } catch (e) {
      throw Exception('OCR处理错误: $e');
    }
  }

  static Future<String> extractTextFromXFile(XFile imageFile) async {
    if (_endpoint.isEmpty || _apiKey.isEmpty) {
      throw Exception('Azure配置信息缺失，请检查.env文件');
    }

    try {
      final bytes = await imageFile.readAsBytes();
      return await _performOcr(bytes);
    } catch (e) {
      throw Exception('OCR处理错误: $e');
    }
  }

  static Future<String> _performOcr(Uint8List bytes) async {
    final uri = Uri.parse('$_endpoint/vision/v3.2/ocr');

    final response = await http.post(
      uri,
      headers: {
        'Ocp-Apim-Subscription-Key': _apiKey,
        'Content-Type': 'application/octet-stream',
      },
      body: bytes,
    );

    if (response.statusCode == 200) {
      final jsonResponse = json.decode(response.body);
      return _parseOcrResponse(jsonResponse);
    } else {
      throw Exception('OCR请求失败: ${response.statusCode} - ${response.body}');
    }
  }

  static String _parseOcrResponse(Map<String, dynamic> response) {
    final StringBuffer result = StringBuffer();

    if (response['regions'] != null) {
      for (var region in response['regions']) {
        if (region['lines'] != null) {
          for (var line in region['lines']) {
            if (line['words'] != null) {
              final words = <String>[];
              for (var word in line['words']) {
                if (word['text'] != null) {
                  words.add(word['text']);
                }
              }
              if (words.isNotEmpty) {
                result.writeln(words.join(' '));
              }
            }
          }
        }
      }
    }

    return result.toString().trim();
  }
}
