import 'package:flutter/material.dart';
import '../models/detection_history.dart';
import '../models/dish.dart';
import '../services/detection_history_service.dart';

class DetectionHistoryScreen extends StatefulWidget {
  const DetectionHistoryScreen({super.key});

  @override
  State<DetectionHistoryScreen> createState() => _DetectionHistoryScreenState();
}

class _DetectionHistoryScreenState extends State<DetectionHistoryScreen> {
  List<DetectionHistory> _historyList = [];
  DetectionHistoryStats? _stats;
  bool _isLoading = true;
  String _searchQuery = '';
  AllergenRiskLevel? _filterRiskLevel;

  @override
  void initState() {
    super.initState();
    _loadHistory();
  }

  Future<void> _loadHistory() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final history = await DetectionHistoryService.getAllHistory();
      final stats = await DetectionHistoryService.getHistoryStats();

      setState(() {
        _historyList = history;
        _stats = stats;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      _showErrorDialog('加载历史记录失败: $e');
    }
  }

  Future<void> _searchHistory() async {
    if (_searchQuery.isEmpty && _filterRiskLevel == null) {
      await _loadHistory();
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      List<DetectionHistory> results;
      
      if (_searchQuery.isNotEmpty) {
        results = await DetectionHistoryService.searchHistory(_searchQuery);
      } else {
        results = await DetectionHistoryService.getAllHistory();
      }

      if (_filterRiskLevel != null) {
        results = results.where((record) => 
            record.summaryResult.riskLevel == _filterRiskLevel).toList();
      }

      setState(() {
        _historyList = results;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      _showErrorDialog('搜索失败: $e');
    }
  }

  Future<void> _deleteRecord(String recordId) async {
    try {
      await DetectionHistoryService.deleteRecord(recordId);
      await _loadHistory();
      _showSuccessDialog('记录已删除');
    } catch (e) {
      _showErrorDialog('删除失败: $e');
    }
  }

  Future<void> _clearAllHistory() async {
    final confirmed = await _showConfirmDialog(
      '确认清空',
      '确定要清空所有检测历史记录吗？此操作不可撤销。',
    );

    if (confirmed) {
      try {
        await DetectionHistoryService.clearAllHistory();
        await _loadHistory();
        _showSuccessDialog('历史记录已清空');
      } catch (e) {
        _showErrorDialog('清空失败: $e');
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('检测历史'),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
        actions: [
          IconButton(
            onPressed: _clearAllHistory,
            icon: const Icon(Icons.delete_sweep),
            tooltip: '清空历史',
          ),
        ],
      ),
      body: Column(
        children: [
          // 统计信息卡片
          if (_stats != null) _buildStatsCard(),

          // 搜索和筛选
          _buildSearchAndFilter(),

          // 历史记录列表
          Expanded(
            child: _isLoading
                ? const Center(child: CircularProgressIndicator())
                : _historyList.isEmpty
                    ? _buildEmptyState()
                    : _buildHistoryList(),
          ),
        ],
      ),
    );
  }

  Widget _buildStatsCard() {
    final stats = _stats!;
    return Card(
      margin: const EdgeInsets.all(16),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              '统计信息',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Expanded(
                  child: _buildStatItem(
                    '总检测次数',
                    '${stats.totalDetections}',
                    Icons.analytics,
                    Colors.blue,
                  ),
                ),
                Expanded(
                  child: _buildStatItem(
                    '安全检测',
                    '${stats.safeDetections}',
                    Icons.check_circle,
                    Colors.green,
                  ),
                ),
                Expanded(
                  child: _buildStatItem(
                    '风险检测',
                    '${stats.riskyDetections}',
                    Icons.warning,
                    Colors.orange,
                  ),
                ),
              ],
            ),
            if (stats.totalDetections > 0) ...[
              const SizedBox(height: 12),
              LinearProgressIndicator(
                value: stats.safetyRate,
                backgroundColor: Colors.red[100],
                valueColor: AlwaysStoppedAnimation<Color>(Colors.green),
              ),
              const SizedBox(height: 4),
              Text(
                '安全率: ${(stats.safetyRate * 100).toStringAsFixed(1)}%',
                style: const TextStyle(fontSize: 12, color: Colors.grey),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildStatItem(String label, String value, IconData icon, Color color) {
    return Column(
      children: [
        Icon(icon, color: color, size: 24),
        const SizedBox(height: 4),
        Text(
          value,
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
        Text(
          label,
          style: const TextStyle(fontSize: 12, color: Colors.grey),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildSearchAndFilter() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        children: [
          // 搜索框
          TextField(
            decoration: const InputDecoration(
              hintText: '搜索菜品、配料或文本...',
              prefixIcon: Icon(Icons.search),
              border: OutlineInputBorder(),
            ),
            onChanged: (value) {
              setState(() {
                _searchQuery = value;
              });
              _searchHistory();
            },
          ),
          const SizedBox(height: 8),
          
          // 风险等级筛选
          SingleChildScrollView(
            scrollDirection: Axis.horizontal,
            child: Row(
              children: [
                const Text('筛选: '),
                const SizedBox(width: 8),
                _buildFilterChip('全部', null),
                _buildFilterChip('安全', AllergenRiskLevel.safe),
                _buildFilterChip('低风险', AllergenRiskLevel.low),
                _buildFilterChip('中风险', AllergenRiskLevel.medium),
                _buildFilterChip('高风险', AllergenRiskLevel.high),
                _buildFilterChip('未知', AllergenRiskLevel.unknown),
              ],
            ),
          ),
          const SizedBox(height: 16),
        ],
      ),
    );
  }

  Widget _buildFilterChip(String label, AllergenRiskLevel? riskLevel) {
    final isSelected = _filterRiskLevel == riskLevel;
    return Padding(
      padding: const EdgeInsets.only(right: 8),
      child: FilterChip(
        label: Text(label),
        selected: isSelected,
        onSelected: (selected) {
          setState(() {
            _filterRiskLevel = selected ? riskLevel : null;
          });
          _searchHistory();
        },
        backgroundColor: riskLevel?.color.withOpacity(0.1),
        selectedColor: riskLevel?.color.withOpacity(0.3),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.history,
            size: 64,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            _searchQuery.isNotEmpty || _filterRiskLevel != null
                ? '没有找到匹配的记录'
                : '还没有检测历史记录',
            style: TextStyle(
              fontSize: 18,
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 8),
          Text(
            _searchQuery.isNotEmpty || _filterRiskLevel != null
                ? '尝试调整搜索条件'
                : '开始使用过敏原检测功能吧',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey[500],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildHistoryList() {
    return ListView.builder(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      itemCount: _historyList.length,
      itemBuilder: (context, index) {
        final record = _historyList[index];
        return _buildHistoryItem(record);
      },
    );
  }

  Widget _buildHistoryItem(DetectionHistory record) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: InkWell(
        onTap: () => _showRecordDetails(record),
        borderRadius: BorderRadius.circular(8),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 标题行
              Row(
                children: [
                  Expanded(
                    child: Text(
                      record.shortDescription,
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  // 风险等级标识
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: record.riskColor.withOpacity(0.2),
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(
                        color: record.riskColor.withOpacity(0.5),
                      ),
                    ),
                    child: Text(
                      record.summaryResult.riskLevel.displayName,
                      style: TextStyle(
                        fontSize: 12,
                        color: record.riskColor,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  // 删除按钮
                  IconButton(
                    onPressed: () => _deleteRecord(record.id),
                    icon: const Icon(Icons.delete_outline),
                    iconSize: 20,
                    color: Colors.grey,
                  ),
                ],
              ),
              const SizedBox(height: 8),
              
              // 检测结果摘要
              Text(
                record.summaryResult.warning,
                style: TextStyle(
                  fontSize: 14,
                  color: record.hasSafetyRisk ? Colors.red[700] : Colors.green[700],
                ),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
              const SizedBox(height: 8),
              
              // 底部信息
              Row(
                children: [
                  Icon(Icons.access_time, size: 16, color: Colors.grey[600]),
                  const SizedBox(width: 4),
                  Text(
                    record.formattedTime,
                    style: TextStyle(fontSize: 12, color: Colors.grey[600]),
                  ),
                  const SizedBox(width: 16),
                  Icon(Icons.restaurant, size: 16, color: Colors.grey[600]),
                  const SizedBox(width: 4),
                  Text(
                    '${record.totalDishCount}道菜',
                    style: TextStyle(fontSize: 12, color: Colors.grey[600]),
                  ),
                  if (record.totalAllergenCount > 0) ...[
                    const SizedBox(width: 16),
                    Icon(Icons.warning, size: 16, color: Colors.orange[600]),
                    const SizedBox(width: 4),
                    Text(
                      '${record.totalAllergenCount}个过敏原',
                      style: TextStyle(fontSize: 12, color: Colors.orange[600]),
                    ),
                  ],
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _showRecordDetails(DetectionHistory record) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(record.shortDescription),
        content: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Text('检测时间: ${record.timestamp.toString().substring(0, 19)}'),
              const SizedBox(height: 8),
              Text('检测来源: ${record.source.displayName}'),
              const SizedBox(height: 8),
              Text('原始文本: ${record.originalText}'),
              const SizedBox(height: 8),
              Text('检测结果: ${record.summaryResult.warning}'),
              if (record.summaryResult.suggestions.isNotEmpty) ...[
                const SizedBox(height: 8),
                const Text('建议:'),
                ...record.summaryResult.suggestions.map((suggestion) => 
                    Text('• $suggestion')),
              ],
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('关闭'),
          ),
        ],
      ),
    );
  }

  Future<bool> _showConfirmDialog(String title, String content) async {
    final result = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(title),
        content: Text(content),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            child: const Text('确定'),
          ),
        ],
      ),
    );
    return result ?? false;
  }

  void _showSuccessDialog(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.green,
      ),
    );
  }

  void _showErrorDialog(String message) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('错误'),
        content: Text(message),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('确定'),
          ),
        ],
      ),
    );
  }
}
