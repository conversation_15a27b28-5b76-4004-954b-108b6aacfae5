@echo off
echo === Azure OCR Flutter App Setup ===
echo.

REM 检查并创建.env文件
if not exist ".env" (
    if exist ".env.example" (
        echo Creating .env file from template...
        copy ".env.example" ".env" >nul
        echo ✓ .env file created from .env.example
        echo ⚠ Please edit .env file and add your Azure configuration
    ) else (
        echo ✗ .env.example template not found
    )
) else (
    echo ✓ .env file already exists
)

echo.
echo Installing Flutter dependencies...
C:\flutter\bin\flutter pub get
echo.
echo Flutter dependencies installed successfully!
echo.
echo To run the app:
echo C:\flutter\bin\flutter run
echo.
echo To run tests:
echo C:\flutter\bin\flutter test
echo.
echo Remember to configure your Azure settings in .env file!
pause
