# Azure OCR App 构建说明

## 📱 Android APK (已完成)

✅ **Android APK已成功生成！**

**文件位置**: `build/app/outputs/flutter-apk/app-release.apk` (19.9MB)

### 安装方法
1. 将APK文件传输到Android设备
2. 在设备上启用"未知来源"安装
3. 点击APK文件进行安装

## 🍎 iOS 构建说明

由于在Windows环境下无法直接构建iOS应用，需要在macOS环境中完成。

### 在macOS上构建iOS应用的步骤：

1. **环境准备**
   ```bash
   # 确保已安装Xcode和Flutter
   flutter doctor
   ```

2. **获取项目代码**
   ```bash
   # 将整个项目文件夹复制到macOS设备
   ```

3. **安装依赖**
   ```bash
   flutter pub get
   cd ios
   pod install
   ```

4. **构建iOS应用**
   ```bash
   # 构建用于发布的IPA文件
   flutter build ios --release
   
   # 或者构建用于开发的应用
   flutter build ios
   ```

5. **生成IPA文件**
   ```bash
   # 在Xcode中打开项目
   open ios/Runner.xcworkspace
   
   # 在Xcode中：
   # 1. 选择 Product > Archive
   # 2. 选择 Distribute App
   # 3. 选择分发方式（App Store、Ad Hoc等）
   ```

## 🔧 项目配置

### Azure配置
- 项目已配置为在发布版本中使用硬编码的Azure配置
- 开发环境仍使用`.env`文件
- API密钥和端点已内置到应用中

### 权限配置
- **Android**: 已配置相机和存储权限
- **iOS**: 已配置相机和相册访问权限

### 功能特性
- ✅ 图片选择（相册/文件）
- ✅ 相机拍摄
- ✅ Azure OCR文字识别
- ✅ 中文文字支持
- ✅ 结果复制功能
- ✅ 连接状态检测

## 📋 技术规格

### Android APK
- **最小SDK版本**: API 21 (Android 5.0)
- **目标SDK版本**: API 35 (Android 15)
- **架构支持**: ARM64, ARMv7, x86_64
- **文件大小**: 19.9MB

### iOS应用
- **最小iOS版本**: iOS 12.0+
- **架构支持**: ARM64 (iPhone 5s及以上)
- **需要权限**: 相机、相册访问

## 🚀 部署建议

### Android
- 可以直接分发APK文件
- 建议上传到Google Play Store进行正式发布

### iOS
- 需要Apple Developer账号
- 建议通过App Store进行分发
- 可以使用TestFlight进行内测

## 📞 技术支持

如需技术支持或有任何问题，请联系开发团队。

---
**构建时间**: $(Get-Date)
**Flutter版本**: 请运行 `flutter --version` 查看
**项目版本**: 1.0.0+1
