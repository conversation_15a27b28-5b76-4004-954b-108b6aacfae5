import 'package:flutter/foundation.dart';

class MultilingualTextProcessor {
  // 检测文本语言
  static String detectLanguage(String text) {
    if (text.isEmpty) return 'en';
    
    // 统计不同语言字符的比例
    int chineseChars = 0;
    int englishChars = 0;
    int germanChars = 0;
    
    for (int i = 0; i < text.length; i++) {
      final char = text[i];
      final codeUnit = char.codeUnitAt(0);
      
      // 中文字符范围
      if ((codeUnit >= 0x4E00 && codeUnit <= 0x9FFF) ||
          (codeUnit >= 0x3400 && codeUnit <= 0x4DBF)) {
        chineseChars++;
      }
      // 英文字符
      else if ((codeUnit >= 65 && codeUnit <= 90) ||
               (codeUnit >= 97 && codeUnit <= 122)) {
        englishChars++;
      }
      // 德语特殊字符
      else if (char == 'ä' || char == 'ö' || char == 'ü' || 
               char == 'Ä' || char == 'Ö' || char == 'Ü' || char == 'ß') {
        germanChars++;
      }
    }
    
    // 判断主要语言
    if (chineseChars > englishChars && chineseChars > germanChars) {
      return 'zh';
    } else if (germanChars > 0) {
      return 'de';
    } else {
      return 'en';
    }
  }
  
  // 根据语言清洗OCR文本
  static String cleanOcrTextByLanguage(String text, String language) {
    if (kDebugMode) {
      print('🌍 多语言文本处理: "$text" (语言: $language)');
    }
    
    switch (language) {
      case 'zh':
        return _cleanChineseText(text);
      case 'en':
        return _cleanEnglishText(text);
      case 'de':
        return _cleanGermanText(text);
      default:
        return _cleanEnglishText(text);
    }
  }
  
  // 中文文本清洗
  static String _cleanChineseText(String text) {
    var cleaned = text.trim();
    
    // 去除空格
    cleaned = cleaned.replaceAll(RegExp(r'\s+'), '');
    
    // 去除中文标点符号
    cleaned = cleaned.replaceAll(RegExp(r'[，。！？；：""''（）【】《》〈〉]'), '');
    
    // 去除价格相关符号
    cleaned = cleaned.replaceAll(RegExp(r'[￥¥元兀]'), '');
    cleaned = cleaned.replaceAll(RegExp(r'\d+\.?\d*'), '');
    
    // 中文OCR纠错
    final chineseCorrections = {
      '兀': '元', '机': '鸡', '保': '保', '官': '宫',
      '马': '麻', '波': '婆', '斗': '豆', '府': '腐',
    };
    
    chineseCorrections.forEach((wrong, correct) {
      cleaned = cleaned.replaceAll(wrong, correct);
    });
    
    if (kDebugMode) {
      print('  中文清洗结果: "$cleaned"');
    }
    
    return cleaned;
  }
  
  // 英文文本清洗
  static String _cleanEnglishText(String text) {
    var cleaned = text.trim().toLowerCase();
    
    // 去除多余空格，但保留单词间的空格
    cleaned = cleaned.replaceAll(RegExp(r'\s+'), ' ');
    
    // 去除英文标点符号
    cleaned = cleaned.replaceAll(RegExp(r'[,.;:!?"\(\)\[\]{}]'), '');
    
    // 去除价格符号
    cleaned = cleaned.replaceAll(RegExp(r'[\$£€]'), '');
    cleaned = cleaned.replaceAll(RegExp(r'\b\d+\.?\d*\b'), '');
    
    // 去除常见的菜单噪音词
    final noiseWords = ['menu', 'price', 'special', 'today', 'fresh', 'new', 'hot'];
    for (final noise in noiseWords) {
      cleaned = cleaned.replaceAll(RegExp(r'\b' + noise + r'\b'), '');
    }
    
    // 英文OCR纠错
    final englishCorrections = {
      'chickcn': 'chicken',
      'chiken': 'chicken',
      'chickn': 'chicken',
      'beaf': 'beef',
      'beff': 'beef',
      'pork': 'pork',
      'fisb': 'fish',
      'fiah': 'fish',
      'burgcr': 'burger',
      'burgcr': 'burger',
      'pizza': 'pizza',
      'pizze': 'pizza',
      'salad': 'salad',
      'selad': 'salad',
      'soup': 'soup',
      'sonp': 'soup',
    };
    
    englishCorrections.forEach((wrong, correct) {
      cleaned = cleaned.replaceAll(wrong, correct);
    });
    
    // 去除多余空格
    cleaned = cleaned.trim().replaceAll(RegExp(r'\s+'), ' ');
    
    if (kDebugMode) {
      print('  英文清洗结果: "$cleaned"');
    }
    
    return cleaned;
  }
  
  // 德文文本清洗
  static String _cleanGermanText(String text) {
    var cleaned = text.trim().toLowerCase();
    
    // 保留德语特殊字符
    cleaned = cleaned.replaceAll(RegExp(r'\s+'), ' ');
    
    // 去除德文标点符号
    cleaned = cleaned.replaceAll(RegExp(r'[,.;:!?"\(\)\[\]{}]'), '');
    
    // 去除价格符号
    cleaned = cleaned.replaceAll(RegExp(r'[€]'), '');
    cleaned = cleaned.replaceAll(RegExp(r'\b\d+[,.]?\d*\b'), '');
    
    // 去除德语菜单噪音词
    final noiseWords = ['speisekarte', 'preis', 'spezial', 'heute', 'frisch', 'neu', 'heiß'];
    for (final noise in noiseWords) {
      cleaned = cleaned.replaceAll(RegExp(r'\b' + noise + r'\b'), '');
    }
    
    // 德文OCR纠错
    final germanCorrections = {
      'hähnchen': 'hähnchen',
      'hahnchen': 'hähnchen',
      'rindfleisch': 'rindfleisch',
      'rindflcisch': 'rindfleisch',
      'schweinefleisch': 'schweinefleisch',
      'fisch': 'fisch',
      'fiach': 'fisch',
      'wurst': 'wurst',
      'wuret': 'wurst',
      'schnitzel': 'schnitzel',
      'acbnitzel': 'schnitzel',
      'suppe': 'suppe',
      'auppe': 'suppe',
    };
    
    germanCorrections.forEach((wrong, correct) {
      cleaned = cleaned.replaceAll(wrong, correct);
    });
    
    cleaned = cleaned.trim().replaceAll(RegExp(r'\s+'), ' ');
    
    if (kDebugMode) {
      print('  德文清洗结果: "$cleaned"');
    }
    
    return cleaned;
  }
  
  // 智能提取菜品名称（多语言）
  static List<String> extractDishNamesMultilingual(List<String> ocrLines) {
    final dishNames = <String>[];
    
    if (kDebugMode) {
      print('🌍 开始多语言菜品提取，共 ${ocrLines.length} 行文本');
    }
    
    for (int i = 0; i < ocrLines.length; i++) {
      final line = ocrLines[i];
      
      if (line.trim().isEmpty || line.length < 2) continue;
      
      // 检测语言
      final language = detectLanguage(line);
      
      // 根据语言清洗文本
      final cleanedLine = cleanOcrTextByLanguage(line, language);
      
      if (cleanedLine.isEmpty || cleanedLine.length < 2) continue;
      
      // 根据语言判断是否为菜品
      if (_isLikelyDishNameByLanguage(cleanedLine, language)) {
        dishNames.add(cleanedLine);
        
        if (kDebugMode) {
          print('  ✅ 提取菜品: "$cleanedLine" (语言: $language)');
        }
      }
    }
    
    final uniqueDishNames = dishNames.toSet().toList();
    
    if (kDebugMode) {
      print('🎯 最终提取的菜品: $uniqueDishNames');
    }
    
    return uniqueDishNames;
  }
  
  // 根据语言判断是否为菜品名称
  static bool _isLikelyDishNameByLanguage(String text, String language) {
    switch (language) {
      case 'zh':
        return _isLikelyChineseDish(text);
      case 'en':
        return _isLikelyEnglishDish(text);
      case 'de':
        return _isLikelyGermanDish(text);
      default:
        return _isLikelyEnglishDish(text);
    }
  }
  
  static bool _isLikelyChineseDish(String text) {
    // 中文菜品判断逻辑
    final dishKeywords = ['鸡', '鱼', '肉', '虾', '蟹', '菜', '汤', '饭', '面', '粥'];
    return dishKeywords.any((keyword) => text.contains(keyword));
  }
  
  static bool _isLikelyEnglishDish(String text) {
    // 英文菜品判断逻辑
    final dishKeywords = ['chicken', 'beef', 'pork', 'fish', 'burger', 'pizza', 'salad', 'soup', 'pasta', 'rice'];
    return dishKeywords.any((keyword) => text.toLowerCase().contains(keyword));
  }
  
  static bool _isLikelyGermanDish(String text) {
    // 德文菜品判断逻辑
    final dishKeywords = ['hähnchen', 'rindfleisch', 'schweinefleisch', 'fisch', 'wurst', 'schnitzel', 'suppe', 'salat'];
    return dishKeywords.any((keyword) => text.toLowerCase().contains(keyword));
  }
}
