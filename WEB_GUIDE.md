# Azure OCR Web版使用指南

## 🌐 Web版特性

Web版本专为浏览器环境优化，具有以下特点：

- ✅ **无需安装** - 直接在浏览器中运行
- ✅ **跨平台** - 支持Windows、macOS、Linux
- ✅ **文件上传** - 支持拖拽上传图片文件
- ✅ **实时预览** - 即时显示选择的图片
- ✅ **连接状态** - 实时显示Azure连接状态
- ✅ **复制功能** - 一键复制识别结果

## 🚀 快速启动

### 方法1：使用批处理脚本
```cmd
# 双击运行
run_web.bat
```

### 方法2：使用PowerShell脚本
```powershell
powershell -ExecutionPolicy Bypass -File run_web.ps1
```

### 方法3：手动启动
```cmd
# 1. 安装依赖
flutter pub get

# 2. 启动Web服务器
flutter run -d web-server --web-port 8080
```

## 🔧 配置要求

### 1. Flutter Web支持
确保Flutter已启用Web支持：
```cmd
flutter config --enable-web
```

### 2. Azure配置
确保`.env`文件已正确配置：
```env
AZURE_VISION_ENDPOINT=https://your-resource.cognitiveservices.azure.com/
AZURE_VISION_KEY=your-api-key-here
```

### 3. 浏览器要求
- **推荐浏览器**: Chrome、Firefox、Edge、Safari
- **最低版本**: 支持ES6的现代浏览器
- **HTTPS**: 某些功能需要HTTPS环境

## 📱 使用说明

### 1. 启动应用
运行启动脚本后，应用会自动在浏览器中打开：
```
http://localhost:8080
```

### 2. 检查连接状态
- 🟢 **绿色图标**: Azure连接正常
- 🔴 **红色图标**: Azure连接失败

### 3. 上传图片
- 点击"选择图片文件"按钮
- 选择JPG、PNG、BMP、GIF格式的图片
- 图片会立即显示在预览区域

### 4. 文字识别
- 确保Azure连接正常（绿色状态）
- 点击"开始文字识别"按钮
- 等待处理完成

### 5. 查看结果
- 识别结果会显示在下方文本框中
- 点击"复制"按钮复制文本到剪贴板
- 文本支持选择和手动复制

## 🔍 支持的图片格式

| 格式 | 扩展名 | 最大尺寸 | 说明 |
|------|--------|----------|------|
| JPEG | .jpg, .jpeg | 50MB | 推荐格式，压缩率高 |
| PNG | .png | 50MB | 支持透明背景 |
| BMP | .bmp | 50MB | 无压缩格式 |
| GIF | .gif | 50MB | 支持动画（仅识别第一帧） |

## ⚡ 性能优化建议

### 1. 图片优化
- **分辨率**: 建议1920x1080以下
- **文件大小**: 建议5MB以下
- **清晰度**: 确保文字清晰可见

### 2. 网络优化
- 使用稳定的网络连接
- 避免在网络高峰期使用
- 考虑使用CDN加速

### 3. 浏览器优化
- 关闭不必要的标签页
- 清理浏览器缓存
- 使用最新版本浏览器

## 🛠️ 故障排除

### 问题1: 应用无法启动
**解决方案**:
```cmd
# 检查Flutter Web支持
flutter config --enable-web

# 清理并重新安装
flutter clean
flutter pub get
```

### 问题2: Azure连接失败
**解决方案**:
1. 检查`.env`文件配置
2. 验证Azure API密钥有效性
3. 确认网络连接正常

### 问题3: 图片上传失败
**解决方案**:
1. 检查图片格式是否支持
2. 确认文件大小不超过50MB
3. 尝试使用其他图片

### 问题4: 识别结果为空
**解决方案**:
1. 确保图片包含清晰的文字
2. 尝试调整图片亮度和对比度
3. 使用更高分辨率的图片

### 问题5: 复制功能不工作
**解决方案**:
1. 使用HTTPS访问（某些浏览器要求）
2. 手动选择文本进行复制
3. 检查浏览器权限设置

## 🔒 安全注意事项

### 1. HTTPS使用
- 生产环境建议使用HTTPS
- 某些浏览器功能需要安全上下文

### 2. API密钥保护
- 不要在客户端暴露API密钥
- 考虑使用代理服务器

### 3. 数据隐私
- 上传的图片仅用于OCR处理
- 不会在本地存储敏感数据

## 📊 开发者信息

### 调试模式
启用调试模式查看详细日志：
```cmd
flutter run -d web-server --web-port 8080 --debug
```

### 构建生产版本
```cmd
flutter build web
```

### 自定义端口
```cmd
flutter run -d web-server --web-port 3000
```

## 🌟 高级功能

### 1. 批量处理
- 可以连续上传多张图片
- 每次处理一张图片

### 2. 结果导出
- 支持复制到剪贴板
- 可以保存为文本文件

### 3. 历史记录
- 当前会话保留处理历史
- 刷新页面会清空历史

## 📞 技术支持

如果遇到问题：

1. **查看控制台**: 按F12打开开发者工具
2. **检查网络**: 查看Network标签页的请求状态
3. **查看日志**: 检查Console标签页的错误信息
4. **重启服务**: 停止并重新启动Web服务器

## 🔄 更新说明

定期更新应用以获得最新功能：
```cmd
flutter upgrade
flutter pub upgrade
```
