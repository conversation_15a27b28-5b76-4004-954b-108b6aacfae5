<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Azure OCR Web测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .status {
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .success { background-color: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .warning { background-color: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        .error { background-color: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background-color: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background-color: #0056b3; }
        .code {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 3px;
            padding: 10px;
            font-family: monospace;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🌐 Azure OCR Web版测试页面</h1>
        
        <div class="info">
            <strong>📋 当前状态检查</strong><br>
            这个页面可以帮助您验证Web环境是否正常工作。
        </div>

        <h2>🔧 环境检查</h2>
        <div id="browser-check" class="status info">
            <strong>浏览器:</strong> <span id="browser-info">检查中...</span>
        </div>
        
        <div id="js-check" class="status success">
            <strong>JavaScript:</strong> ✓ 正常工作
        </div>

        <h2>🚀 Flutter Web启动方法</h2>
        
        <h3>方法1: 使用简化启动器</h3>
        <div class="code">
            双击运行: launch_web_simple.bat
        </div>

        <h3>方法2: 手动命令</h3>
        <div class="code">
            flutter run -d chrome
        </div>

        <h3>方法3: 使用Web服务器</h3>
        <div class="code">
            flutter run -d web-server --web-port 3000
        </div>

        <h2>📱 预期功能</h2>
        <ul>
            <li>✅ 文件上传选择图片</li>
            <li>✅ Azure OCR文字识别</li>
            <li>✅ 实时连接状态显示</li>
            <li>✅ 识别结果复制功能</li>
        </ul>

        <h2>🔍 故障排除</h2>
        
        <div class="warning">
            <strong>⚠️ 常见问题:</strong><br>
            • file_picker警告 - 可以忽略<br>
            • 端口被占用 - 尝试其他端口<br>
            • 权限问题 - 以管理员身份运行
        </div>

        <h2>🧪 测试步骤</h2>
        <ol>
            <li>确保.env文件已配置Azure信息</li>
            <li>运行Flutter Web启动命令</li>
            <li>等待Chrome浏览器自动打开</li>
            <li>检查Azure连接状态（绿色=正常）</li>
            <li>上传测试图片</li>
            <li>点击"开始文字识别"</li>
            <li>查看识别结果</li>
        </ol>

        <h2>📞 需要帮助？</h2>
        <p>如果遇到问题，请检查:</p>
        <ul>
            <li>浏览器控制台错误信息 (按F12)</li>
            <li>网络连接状态</li>
            <li>Azure API配置</li>
            <li>Flutter版本兼容性</li>
        </ul>

        <div class="success">
            <strong>🎯 成功标志:</strong><br>
            当您看到"Azure OCR 文字识别 (Web版)"标题和绿色的Azure连接状态时，说明应用已成功启动！
        </div>
    </div>

    <script>
        // 检查浏览器信息
        document.getElementById('browser-info').textContent = navigator.userAgent;
        
        // 检查本地存储
        try {
            localStorage.setItem('test', 'test');
            localStorage.removeItem('test');
        } catch(e) {
            document.getElementById('browser-check').className = 'status warning';
            document.getElementById('browser-check').innerHTML = '<strong>浏览器:</strong> ⚠️ 本地存储受限';
        }
    </script>
</body>
</html>
