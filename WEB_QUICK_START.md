# Azure OCR Web版快速启动指南

## 🚀 解决当前问题

您遇到的问题和解决方案：

### 问题1: Web支持未配置
✅ **已解决** - 已运行 `flutter create . --platforms web`

### 问题2: 端口权限问题
✅ **已解决** - 改用端口3000而不是8080

### 问题3: file_picker警告
⚠️ **可忽略** - 这些警告不影响Web功能

## 📋 启动步骤

### 方法1: 使用新的快速启动脚本
```cmd
start_web.bat
```

### 方法2: 手动启动（推荐）
```cmd
# 1. 确保.env文件存在并配置正确
copy .env.example .env
# 编辑.env文件添加Azure配置

# 2. 启用Web支持
flutter config --enable-web

# 3. 清理并重新获取依赖
flutter clean
flutter pub get

# 4. 启动Web服务器
flutter run -d web-server --web-port 3000
```

### 方法3: 使用Chrome浏览器
```cmd
flutter run -d chrome --web-port 3000
```

## 🔧 故障排除

### 如果端口3000被占用
```cmd
# 尝试其他端口
flutter run -d web-server --web-port 4000
flutter run -d web-server --web-port 5000
flutter run -d web-server --web-port 8000
```

### 如果权限问题持续
1. **以管理员身份运行命令提示符**
2. **或者使用Chrome直接启动**：
   ```cmd
   flutter run -d chrome
   ```

### 如果Web支持问题
```cmd
# 检查Flutter配置
flutter doctor

# 重新启用Web支持
flutter config --enable-web

# 重新创建Web文件
flutter create . --platforms web
```

## 🌐 访问应用

启动成功后，在浏览器中访问：
- http://localhost:3000 (默认)
- http://localhost:4000 (备用)
- 或Flutter输出中显示的URL

## 📱 Web版功能测试

1. **连接状态检查**
   - 页面顶部应显示Azure连接状态
   - 绿色 = 连接成功
   - 红色 = 连接失败

2. **文件上传测试**
   - 点击"选择图片文件"
   - 选择JPG/PNG图片
   - 图片应显示在预览区域

3. **OCR功能测试**
   - 确保Azure连接正常
   - 点击"开始文字识别"
   - 查看识别结果

## 🔍 调试信息

### 查看浏览器控制台
1. 按F12打开开发者工具
2. 查看Console标签页
3. 检查网络请求状态

### 常见错误和解决方案

**错误**: `SocketException: Failed to create server socket`
**解决**: 更换端口或以管理员身份运行

**错误**: `This application is not configured to build on the web`
**解决**: 运行 `flutter create . --platforms web`

**错误**: Azure连接失败
**解决**: 检查.env文件配置

## 📞 需要帮助？

如果仍有问题，请提供以下信息：
1. 运行命令的完整输出
2. 浏览器控制台的错误信息
3. .env文件配置状态（隐藏敏感信息）

## 🎯 下一步

Web版启动成功后：
1. 测试Azure OCR功能
2. 尝试不同格式的图片
3. 检查识别准确性
4. 测试复制功能

---

💡 **提示**: file_picker的警告信息可以安全忽略，不会影响Web版本的核心功能。
