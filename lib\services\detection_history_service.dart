import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:universal_html/html.dart' as html;
import '../models/detection_history.dart';
import '../models/dish.dart';

// 检测历史记录服务
class DetectionHistoryService {
  static const String _storageKey = 'detection_history';
  static const int _maxHistoryCount = 100; // 最大保存记录数
  static List<DetectionHistory> _historyCache = [];
  static bool _isLoaded = false;

  // 获取所有历史记录
  static Future<List<DetectionHistory>> getAllHistory() async {
    if (!_isLoaded) {
      await _loadFromStorage();
    }
    return List.from(_historyCache);
  }

  // 同步获取所有历史记录
  static List<DetectionHistory> getAllHistorySync() {
    return List.from(_historyCache);
  }

  // 添加新的检测记录
  static Future<void> addDetectionRecord({
    required String originalText,
    required List<String> extractedDishes,
    required List<AllergenDetectionResult> detectionResults,
    required AllergenDetectionResult summaryResult,
    String? imagePath,
    DetectionSource source = DetectionSource.ocr,
  }) async {
    try {
      // 确保已加载历史记录
      if (!_isLoaded) {
        await _loadFromStorage();
      }

      // 创建新记录
      final newRecord = DetectionHistory(
        id: _generateId(),
        timestamp: DateTime.now(),
        originalText: originalText,
        extractedDishes: extractedDishes,
        detectionResults: detectionResults,
        summaryResult: summaryResult,
        imagePath: imagePath,
        source: source,
      );

      // 添加到缓存开头（最新的在前面）
      _historyCache.insert(0, newRecord);

      // 限制历史记录数量
      if (_historyCache.length > _maxHistoryCount) {
        _historyCache = _historyCache.take(_maxHistoryCount).toList();
      }

      // 保存到存储
      await _saveToStorage();

      if (kDebugMode) {
        print('📝 新增检测记录: ${newRecord.shortDescription}');
        print('   检测结果: ${summaryResult.warning}');
        print('   历史记录总数: ${_historyCache.length}');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ 添加检测记录失败: $e');
      }
    }
  }

  // 删除指定记录
  static Future<void> deleteRecord(String recordId) async {
    try {
      if (!_isLoaded) {
        await _loadFromStorage();
      }

      final originalCount = _historyCache.length;
      _historyCache.removeWhere((record) => record.id == recordId);

      if (_historyCache.length < originalCount) {
        await _saveToStorage();
        if (kDebugMode) {
          print('🗑️ 删除检测记录: $recordId');
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ 删除检测记录失败: $e');
      }
    }
  }

  // 清空所有历史记录
  static Future<void> clearAllHistory() async {
    try {
      _historyCache.clear();
      await _saveToStorage();
      if (kDebugMode) {
        print('🧹 清空所有检测历史记录');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ 清空历史记录失败: $e');
      }
    }
  }

  // 获取历史记录统计信息
  static Future<DetectionHistoryStats> getHistoryStats() async {
    if (!_isLoaded) {
      await _loadFromStorage();
    }

    final totalDetections = _historyCache.length;
    final safeDetections = _historyCache
        .where((record) => !record.hasSafetyRisk)
        .length;
    final riskyDetections = totalDetections - safeDetections;

    // 统计过敏原出现频率
    final allergenFrequency = <String, int>{};
    for (var record in _historyCache) {
      for (var allergen in record.summaryResult.matchedAllergens) {
        allergenFrequency[allergen] = (allergenFrequency[allergen] ?? 0) + 1;
      }
    }

    // 统计菜品出现频率
    final dishFrequency = <String, int>{};
    for (var record in _historyCache) {
      for (var dish in record.extractedDishes) {
        dishFrequency[dish] = (dishFrequency[dish] ?? 0) + 1;
      }
    }

    final lastDetectionTime = _historyCache.isNotEmpty 
        ? _historyCache.first.timestamp 
        : null;

    return DetectionHistoryStats(
      totalDetections: totalDetections,
      safeDetections: safeDetections,
      riskyDetections: riskyDetections,
      allergenFrequency: allergenFrequency,
      dishFrequency: dishFrequency,
      lastDetectionTime: lastDetectionTime,
    );
  }

  // 按日期范围获取历史记录
  static Future<List<DetectionHistory>> getHistoryByDateRange(
      DateTime startDate, DateTime endDate) async {
    if (!_isLoaded) {
      await _loadFromStorage();
    }

    return _historyCache.where((record) {
      return record.timestamp.isAfter(startDate) &&
          record.timestamp.isBefore(endDate.add(const Duration(days: 1)));
    }).toList();
  }

  // 按风险等级筛选历史记录
  static Future<List<DetectionHistory>> getHistoryByRiskLevel(
      AllergenRiskLevel riskLevel) async {
    if (!_isLoaded) {
      await _loadFromStorage();
    }

    return _historyCache.where((record) {
      return record.summaryResult.riskLevel == riskLevel;
    }).toList();
  }

  // 搜索历史记录
  static Future<List<DetectionHistory>> searchHistory(String query) async {
    if (!_isLoaded) {
      await _loadFromStorage();
    }

    final lowerQuery = query.toLowerCase();
    return _historyCache.where((record) {
      return record.originalText.toLowerCase().contains(lowerQuery) ||
          record.extractedDishes.any((dish) => 
              dish.toLowerCase().contains(lowerQuery)) ||
          record.summaryResult.detectedIngredients.any((ingredient) => 
              ingredient.toLowerCase().contains(lowerQuery));
    }).toList();
  }

  // 从存储加载历史记录
  static Future<void> _loadFromStorage() async {
    try {
      if (kDebugMode) {
        print('🔄 开始加载检测历史记录...');
      }

      String? jsonString;

      if (kIsWeb) {
        // Web环境使用localStorage
        jsonString = html.window.localStorage[_storageKey];
      } else {
        // 移动端使用SharedPreferences
        final prefs = await SharedPreferences.getInstance();
        jsonString = prefs.getString(_storageKey);
      }

      if (jsonString != null && jsonString.isNotEmpty) {
        final List<dynamic> jsonList = jsonDecode(jsonString);
        _historyCache = jsonList
            .map((json) => DetectionHistory.fromJson(json))
            .toList();

        if (kDebugMode) {
          print('✅ 加载检测历史记录: ${_historyCache.length}条');
        }
      } else {
        _historyCache = [];
        if (kDebugMode) {
          print('📝 首次使用，历史记录为空');
        }
      }

      _isLoaded = true;
    } catch (e) {
      if (kDebugMode) {
        print('❌ 加载检测历史记录失败: $e');
      }
      _historyCache = [];
      _isLoaded = true;
    }
  }

  // 保存历史记录到存储
  static Future<void> _saveToStorage() async {
    try {
      if (kDebugMode) {
        print('💾 开始保存检测历史记录: ${_historyCache.length}条');
      }

      final jsonString = jsonEncode(
          _historyCache.map((record) => record.toJson()).toList());

      if (kIsWeb) {
        // Web环境使用localStorage
        html.window.localStorage[_storageKey] = jsonString;
      } else {
        // 移动端使用SharedPreferences
        final prefs = await SharedPreferences.getInstance();
        await prefs.setString(_storageKey, jsonString);
      }

      if (kDebugMode) {
        print('✅ 检测历史记录保存成功');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ 保存检测历史记录失败: $e');
      }
    }
  }

  // 生成唯一ID
  static String _generateId() {
    return 'detection_${DateTime.now().millisecondsSinceEpoch}';
  }

  // 预加载历史记录（应用启动时调用）
  static Future<void> preloadHistory() async {
    if (!_isLoaded) {
      await _loadFromStorage();
    }
  }
}
