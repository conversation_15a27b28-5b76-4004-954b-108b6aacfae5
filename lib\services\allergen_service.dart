import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:universal_html/html.dart' as html;
import '../models/allergen.dart';

// Web环境下的存储服务
class AllergenService {
  static const String _storageKey = 'user_allergens';
  static List<Allergen> _userAllergens = [];

  // 获取用户的过敏原设置
  static Future<List<Allergen>> getUserAllergens() async {
    if (_userAllergens.isEmpty) {
      await _loadFromStorage();
    }
    return _userAllergens;
  }

  // 同步获取用户的过敏原设置（用于已加载的情况）
  static List<Allergen> getUserAllergensSync() {
    return _userAllergens;
  }

  // 获取所有可选的过敏原（包括常见的和用户自定义的）
  static Future<List<Allergen>> getAllAllergens() async {
    final commonAllergens = CommonAllergens.getCommonAllergens();
    final userAllergens = await getUserAllergens();

    // 合并常见过敏原和用户设置
    final Map<String, Allergen> allergenMap = {};

    // 先添加常见过敏原
    for (var allergen in commonAllergens) {
      allergenMap[allergen.id] = allergen;
    }

    // 更新用户选择状态和添加自定义过敏原
    for (var userAllergen in userAllergens) {
      if (allergenMap.containsKey(userAllergen.id)) {
        // 更新现有过敏原的选择状态
        allergenMap[userAllergen.id] = allergenMap[userAllergen.id]!
            .copyWith(isSelected: userAllergen.isSelected);
      } else {
        // 添加用户自定义的过敏原
        allergenMap[userAllergen.id] = userAllergen;
      }
    }

    return allergenMap.values.toList();
  }

  // 同步获取所有过敏原（用于已加载的情况）
  static List<Allergen> getAllAllergensSync() {
    final commonAllergens = CommonAllergens.getCommonAllergens();
    final userAllergens = getUserAllergensSync();

    // 合并常见过敏原和用户设置
    final Map<String, Allergen> allergenMap = {};

    // 先添加常见过敏原
    for (var allergen in commonAllergens) {
      allergenMap[allergen.id] = allergen;
    }

    // 更新用户选择状态和添加自定义过敏原
    for (var userAllergen in userAllergens) {
      if (allergenMap.containsKey(userAllergen.id)) {
        // 更新现有过敏原的选择状态
        allergenMap[userAllergen.id] = allergenMap[userAllergen.id]!
            .copyWith(isSelected: userAllergen.isSelected);
      } else {
        // 添加用户自定义的过敏原
        allergenMap[userAllergen.id] = userAllergen;
      }
    }

    return allergenMap.values.toList();
  }

  // 获取用户选中的过敏原
  static Future<List<Allergen>> getSelectedAllergens() async {
    final allergens = await getAllAllergens();
    return allergens.where((allergen) => allergen.isSelected).toList();
  }

  // 同步获取用户选中的过敏原（用于已加载的情况）
  static List<Allergen> getSelectedAllergensSync() {
    return getAllAllergensSync()
        .where((allergen) => allergen.isSelected)
        .toList();
  }

  // 更新过敏原选择状态
  static Future<void> updateAllergenSelection(
      String allergenId, bool isSelected) async {
    final allergens = await getAllAllergens();
    final index = allergens.indexWhere((a) => a.id == allergenId);

    if (index != -1) {
      allergens[index] = allergens[index].copyWith(isSelected: isSelected);
      await _saveToStorage(allergens);
    }
  }

  // 添加自定义过敏原
  static Future<void> addCustomAllergen(String name, String category) async {
    final customId = 'custom_${DateTime.now().millisecondsSinceEpoch}';
    final customAllergen = Allergen(
      id: customId,
      name: name,
      category: category,
      description: '用户自定义过敏原',
      isCommon: false,
      isSelected: true,
    );

    final allergens = await getAllAllergens();
    allergens.add(customAllergen);
    await _saveToStorage(allergens);
  }

  // 删除自定义过敏原
  static Future<void> removeCustomAllergen(String allergenId) async {
    if (!allergenId.startsWith('custom_')) return;

    final allergens = await getAllAllergens();
    allergens.removeWhere((a) => a.id == allergenId);
    await _saveToStorage(allergens);
  }

  // 从本地存储加载
  static Future<void> _loadFromStorage() async {
    try {
      if (kDebugMode) {
        print('🔄 开始加载过敏原设置...');
      }

      String? jsonString;

      if (kIsWeb) {
        // Web环境使用localStorage
        jsonString = html.window.localStorage[_storageKey];
        if (kDebugMode) {
          print('🌐 Web环境：从localStorage读取');
        }
      } else {
        // 移动端使用SharedPreferences
        final prefs = await SharedPreferences.getInstance();
        jsonString = prefs.getString(_storageKey);
        if (kDebugMode) {
          print('📱 移动端：从SharedPreferences读取');
        }
      }

      if (kDebugMode) {
        print(
            '📖 从存储读取的数据: ${jsonString?.substring(0, jsonString.length > 100 ? 100 : jsonString.length)}${jsonString != null && jsonString.length > 100 ? '...' : ''}');
      }

      if (jsonString != null && jsonString.isNotEmpty) {
        // 从存储中加载用户设置
        final List<dynamic> jsonList = jsonDecode(jsonString);
        _userAllergens =
            jsonList.map((json) => Allergen.fromJson(json)).toList();

        if (kDebugMode) {
          print('✅ 从存储加载过敏原设置: ${_userAllergens.length}个');
          print(
              '   已选择的过敏原: ${_userAllergens.where((a) => a.isSelected).map((a) => a.name).join(', ')}');
        }
      } else {
        // 首次使用，加载默认设置
        _userAllergens = CommonAllergens.getCommonAllergens();
        if (kDebugMode) {
          print('🆕 首次使用，加载默认过敏原设置: ${_userAllergens.length}个');
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ 加载过敏原设置失败: $e');
        print('   错误详情: ${e.toString()}');
      }
      _userAllergens = CommonAllergens.getCommonAllergens();
    }
  }

  // 保存到本地存储
  static Future<void> _saveToStorage(List<Allergen> allergens) async {
    try {
      _userAllergens = allergens;

      if (kDebugMode) {
        print('💾 开始保存过敏原设置: ${allergens.length}个');
        print(
            '   已选择的过敏原: ${allergens.where((a) => a.isSelected).map((a) => a.name).join(', ')}');
      }

      final jsonString = jsonEncode(allergens.map((a) => a.toJson()).toList());

      if (kDebugMode) {
        print(
            '📝 准备保存的JSON数据: ${jsonString.substring(0, jsonString.length > 100 ? 100 : jsonString.length)}${jsonString.length > 100 ? '...' : ''}');
      }

      if (kIsWeb) {
        // Web环境使用localStorage
        html.window.localStorage[_storageKey] = jsonString;
        if (kDebugMode) {
          print('🌐 Web环境：保存到localStorage');
        }

        // 验证保存是否成功
        final savedString = html.window.localStorage[_storageKey];
        if (kDebugMode) {
          print('✅ 保存成功，验证读取: ${savedString != null ? '成功' : '失败'}');
        }
      } else {
        // 移动端使用SharedPreferences
        final prefs = await SharedPreferences.getInstance();
        await prefs.setString(_storageKey, jsonString);
        if (kDebugMode) {
          print('📱 移动端：保存到SharedPreferences');
        }

        // 验证保存是否成功
        final savedString = prefs.getString(_storageKey);
        if (kDebugMode) {
          print('✅ 保存成功，验证读取: ${savedString != null ? '成功' : '失败'}');
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ 保存过敏原设置失败: $e');
        print('   错误详情: ${e.toString()}');
      }
    }
  }

  // 重置为默认设置
  static Future<void> resetToDefault() async {
    _userAllergens = CommonAllergens.getCommonAllergens();
    await _saveToStorage(_userAllergens);
  }

  // 获取过敏原统计信息
  static Future<Map<String, int>> getAllergenStats() async {
    final allergens = await getAllAllergens();
    final stats = <String, int>{};

    for (var allergen in allergens) {
      stats[allergen.category] = (stats[allergen.category] ?? 0) + 1;
    }

    return stats;
  }
}
