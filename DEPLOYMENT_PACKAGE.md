# Azure OCR App 部署包

## 📦 包含文件

### 🎯 可执行文件
1. **Android APK**: `build/app/outputs/flutter-apk/app-release.apk`
   - 文件大小: 19.9MB
   - 支持Android 5.0+
   - 已签名，可直接安装

### 📱 应用功能
- **图片文字识别**: 使用Azure Computer Vision API
- **多种输入方式**: 相机拍摄、相册选择、文件选择
- **中文支持**: 完美识别中文文字
- **实时预览**: 图片预览和识别结果显示
- **结果操作**: 复制识别结果到剪贴板

### 🔧 技术特性
- **跨平台**: Flutter框架开发
- **云端AI**: Azure Computer Vision服务
- **现代UI**: Material Design界面
- **响应式**: 适配不同屏幕尺寸

## 📋 安装说明

### Android设备安装
1. **下载APK**: 获取 `app-release.apk` 文件
2. **启用安装**: 
   - 设置 > 安全 > 未知来源 (允许)
   - 或 设置 > 应用和通知 > 特殊应用访问 > 安装未知应用
3. **安装应用**: 点击APK文件，按提示安装
4. **授权权限**: 首次使用时授权相机和存储权限

### iOS设备安装
- 需要在macOS环境中构建
- 参考 `BUILD_INSTRUCTIONS.md` 中的详细说明

## 🌟 使用指南

### 首次启动
1. 打开应用
2. 检查Azure连接状态（右上角指示器）
3. 如果显示"Azure已连接"，即可开始使用

### 文字识别流程
1. **选择图片**:
   - 点击"选择图片文件"从相册选择
   - 点击"拍摄照片"使用相机
2. **开始识别**: 点击"开始文字识别"
3. **查看结果**: 识别完成后查看提取的文字
4. **复制结果**: 点击"复制"按钮复制文字

### 支持的图片格式
- JPG/JPEG
- PNG
- BMP
- GIF

## ⚡ 性能优化

### 图片建议
- **分辨率**: 建议1080p以上
- **清晰度**: 确保文字清晰可见
- **光线**: 充足光线下拍摄
- **角度**: 正面拍摄，避免倾斜

### 识别效果
- **中文**: 简体中文识别效果最佳
- **英文**: 支持各种英文字体
- **数字**: 准确识别数字和符号
- **混合**: 支持中英文混合文本

## 🔒 隐私安全

### 数据处理
- 图片仅用于文字识别
- 不会保存或上传个人图片
- 识别结果仅在本地显示

### 网络连接
- 仅连接Azure官方服务
- 使用HTTPS加密传输
- 不收集用户个人信息

## 📞 技术支持

### 常见问题
1. **连接失败**: 检查网络连接
2. **识别不准**: 确保图片清晰
3. **权限问题**: 在设置中授权相机和存储权限

### 联系方式
- 技术支持: 请联系开发团队
- 问题反馈: 提供详细的问题描述和截图

---
**版本**: 1.0.0
**构建日期**: 2024年12月
**兼容性**: Android 5.0+ / iOS 12.0+
