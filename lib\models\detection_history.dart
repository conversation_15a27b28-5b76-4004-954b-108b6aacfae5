import 'package:flutter/material.dart';
import 'dish.dart';

// 检测历史记录数据模型
class DetectionHistory {
  final String id;
  final DateTime timestamp;
  final String originalText; // OCR识别的原始文本
  final List<String> extractedDishes; // 提取的菜品名称
  final List<AllergenDetectionResult> detectionResults; // 每道菜的检测结果
  final AllergenDetectionResult summaryResult; // 汇总结果
  final String? imagePath; // 图片路径（可选）
  final DetectionSource source; // 检测来源

  DetectionHistory({
    required this.id,
    required this.timestamp,
    required this.originalText,
    required this.extractedDishes,
    required this.detectionResults,
    required this.summaryResult,
    this.imagePath,
    required this.source,
  });

  // 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'timestamp': timestamp.toIso8601String(),
      'originalText': originalText,
      'extractedDishes': extractedDishes,
      'detectionResults': detectionResults.map((r) => r.toJson()).toList(),
      'summaryResult': summaryResult.toJson(),
      'imagePath': imagePath,
      'source': source.name,
    };
  }

  // 从JSON创建
  factory DetectionHistory.fromJson(Map<String, dynamic> json) {
    return DetectionHistory(
      id: json['id'],
      timestamp: DateTime.parse(json['timestamp']),
      originalText: json['originalText'],
      extractedDishes: List<String>.from(json['extractedDishes']),
      detectionResults: (json['detectionResults'] as List)
          .map((r) => AllergenDetectionResult.fromJson(r))
          .toList(),
      summaryResult: AllergenDetectionResult.fromJson(json['summaryResult']),
      imagePath: json['imagePath'],
      source: DetectionSource.values.firstWhere(
        (s) => s.name == json['source'],
        orElse: () => DetectionSource.ocr,
      ),
    );
  }

  // 复制并修改
  DetectionHistory copyWith({
    String? id,
    DateTime? timestamp,
    String? originalText,
    List<String>? extractedDishes,
    List<AllergenDetectionResult>? detectionResults,
    AllergenDetectionResult? summaryResult,
    String? imagePath,
    DetectionSource? source,
  }) {
    return DetectionHistory(
      id: id ?? this.id,
      timestamp: timestamp ?? this.timestamp,
      originalText: originalText ?? this.originalText,
      extractedDishes: extractedDishes ?? this.extractedDishes,
      detectionResults: detectionResults ?? this.detectionResults,
      summaryResult: summaryResult ?? this.summaryResult,
      imagePath: imagePath ?? this.imagePath,
      source: source ?? this.source,
    );
  }

  // 获取风险等级颜色
  Color get riskColor => summaryResult.riskLevel.color;

  // 获取检测到的过敏原总数
  int get totalAllergenCount => summaryResult.matchedAllergens.length;

  // 获取检测到的菜品总数
  int get totalDishCount => extractedDishes.length;

  // 是否有安全风险
  bool get hasSafetyRisk => summaryResult.hasSafetyRisk;

  // 获取格式化的时间字符串
  String get formattedTime {
    final now = DateTime.now();
    final difference = now.difference(timestamp);

    if (difference.inDays > 0) {
      return '${difference.inDays}天前';
    } else if (difference.inHours > 0) {
      return '${difference.inHours}小时前';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes}分钟前';
    } else {
      return '刚刚';
    }
  }

  // 获取简短的描述
  String get shortDescription {
    if (extractedDishes.isEmpty) {
      return '未识别到菜品';
    } else if (extractedDishes.length == 1) {
      return extractedDishes.first;
    } else if (extractedDishes.length <= 3) {
      return extractedDishes.join('、');
    } else {
      return '${extractedDishes.take(2).join('、')}等${extractedDishes.length}道菜';
    }
  }
}

// 检测来源枚举
enum DetectionSource {
  ocr('OCR识别'),
  manual('手动输入'),
  test('测试功能');

  const DetectionSource(this.displayName);
  final String displayName;
}

// 扩展AllergenDetectionResult以支持JSON序列化
extension AllergenDetectionResultJson on AllergenDetectionResult {
  Map<String, dynamic> toJson() {
    return {
      'dishName': dishName,
      'detectedIngredients': detectedIngredients,
      'detectedAllergens': detectedAllergens,
      'userAllergens': userAllergens,
      'matchedAllergens': matchedAllergens,
      'riskLevel': riskLevel.name,
      'warning': warning,
      'suggestions': suggestions,
      'detectionSource': detectionSource, // 新增
    };
  }

  static AllergenDetectionResult fromJson(Map<String, dynamic> json) {
    return AllergenDetectionResult(
      dishName: json['dishName'],
      detectedIngredients: List<String>.from(json['detectedIngredients']),
      detectedAllergens: List<String>.from(json['detectedAllergens']),
      userAllergens: List<String>.from(json['userAllergens']),
      matchedAllergens: List<String>.from(json['matchedAllergens']),
      riskLevel: AllergenRiskLevel.values.firstWhere(
        (level) => level.name == json['riskLevel'],
        orElse: () => AllergenRiskLevel.unknown,
      ),
      warning: json['warning'],
      suggestions: List<String>.from(json['suggestions']),
      detectionSource: json['detectionSource'] ?? 'unknown', // 新增
    );
  }
}

// 检测历史统计信息
class DetectionHistoryStats {
  final int totalDetections; // 总检测次数
  final int safeDetections; // 安全检测次数
  final int riskyDetections; // 有风险检测次数
  final Map<String, int> allergenFrequency; // 过敏原出现频率
  final Map<String, int> dishFrequency; // 菜品出现频率
  final DateTime? lastDetectionTime; // 最后检测时间

  DetectionHistoryStats({
    required this.totalDetections,
    required this.safeDetections,
    required this.riskyDetections,
    required this.allergenFrequency,
    required this.dishFrequency,
    this.lastDetectionTime,
  });

  // 安全率
  double get safetyRate {
    if (totalDetections == 0) return 0.0;
    return safeDetections / totalDetections;
  }

  // 风险率
  double get riskRate {
    if (totalDetections == 0) return 0.0;
    return riskyDetections / totalDetections;
  }

  // 最常见的过敏原
  String? get mostCommonAllergen {
    if (allergenFrequency.isEmpty) return null;
    return allergenFrequency.entries
        .reduce((a, b) => a.value > b.value ? a : b)
        .key;
  }

  // 最常检测的菜品
  String? get mostCommonDish {
    if (dishFrequency.isEmpty) return null;
    return dishFrequency.entries
        .reduce((a, b) => a.value > b.value ? a : b)
        .key;
  }
}
