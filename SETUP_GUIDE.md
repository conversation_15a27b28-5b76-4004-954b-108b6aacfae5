# Azure OCR Flutter App 设置指南

## 🚀 快速开始

### 1. 环境检查
确保您的系统已安装：
- Flutter SDK (位于 C:\flutter)
- Android Studio 或 Visual Studio Code
- Git

### 2. 验证Flutter安装
打开命令提示符或PowerShell，运行：
```cmd
C:\flutter\bin\flutter doctor
```

### 3. 安装项目依赖
在项目根目录运行：
```cmd
C:\flutter\bin\flutter pub get
```

或者直接双击 `run_flutter.bat` 文件。

### 4. 配置Azure服务

#### 4.1 创建Azure Computer Vision资源
1. 登录 [Azure门户](https://portal.azure.com/)
2. 点击"创建资源"
3. 搜索"Computer Vision"
4. 选择"Computer Vision"并点击"创建"
5. 填写必要信息：
   - 订阅：选择您的订阅
   - 资源组：创建新的或选择现有的
   - 区域：选择离您最近的区域
   - 名称：为您的资源命名
   - 定价层：选择合适的定价层（F0免费层适合测试）

#### 4.2 获取API密钥和终结点
1. 资源创建完成后，进入资源页面
2. 在左侧菜单中选择"密钥和终结点"
3. 复制以下信息：
   - 终结点 (Endpoint)
   - 密钥1 (Key 1)

#### 4.3 配置.env文件
1. 复制环境变量模板：
```cmd
copy .env.example .env
```

2. 编辑项目根目录的 `.env` 文件：
```env
AZURE_VISION_ENDPOINT=https://your-resource-name.cognitiveservices.azure.com/
AZURE_VISION_KEY=your-subscription-key-here
```

将 `your-resource-name` 和 `your-subscription-key-here` 替换为您的实际值。

⚠️ **安全提醒**: `.env` 文件包含敏感的API密钥，已自动添加到 `.gitignore` 中，不会被提交到Git仓库。

### 5. 运行应用

#### 5.1 连接设备
- **Android**: 通过USB连接Android设备，并启用开发者选项和USB调试
- **iOS**: 连接iPhone/iPad（需要Mac和Xcode）

#### 5.2 检查连接的设备
```cmd
C:\flutter\bin\flutter devices
```

#### 5.3 运行应用
```cmd
C:\flutter\bin\flutter run
```

### 6. 测试应用
```cmd
C:\flutter\bin\flutter test
```

## 📱 使用说明

1. **启动应用** - 应用启动后会显示主界面
2. **选择图片** - 点击"拍照"或"相册"按钮选择包含文字的图片
3. **文字识别** - 点击"开始文字识别"按钮
4. **查看结果** - 识别完成后，文字结果会显示在下方

## 🔧 故障排除

### 常见问题

#### 1. Flutter命令不被识别
**解决方案**: 使用完整路径 `C:\flutter\bin\flutter`

#### 2. "Azure配置信息缺失"错误
**解决方案**: 
- 检查 `.env` 文件是否存在
- 确认Azure终结点和密钥格式正确
- 终结点应以 `https://` 开头，以 `/` 结尾

#### 3. 权限被拒绝
**解决方案**: 
- 在设备设置中手动授予应用相机和存储权限
- 重启应用

#### 4. 网络请求失败
**解决方案**:
- 检查网络连接
- 验证Azure服务状态
- 确认API密钥有效且未过期

#### 5. 依赖安装失败
**解决方案**:
```cmd
C:\flutter\bin\flutter clean
C:\flutter\bin\flutter pub get
```

### 调试模式
如果遇到问题，可以使用调试模式运行：
```cmd
C:\flutter\bin\flutter run --debug
```

## 📞 技术支持

如果您遇到其他问题，请检查：
1. Flutter官方文档: https://flutter.dev/docs
2. Azure Computer Vision文档: https://docs.microsoft.com/azure/cognitive-services/computer-vision/
3. 项目的GitHub Issues页面

## 🎯 下一步

应用运行成功后，您可以：
1. 自定义界面样式
2. 添加更多OCR功能（如表格识别、手写识别等）
3. 集成其他Azure认知服务
4. 添加结果保存和分享功能
