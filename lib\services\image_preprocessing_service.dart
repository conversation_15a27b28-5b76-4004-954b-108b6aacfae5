import 'dart:typed_data';
import 'dart:ui' as ui;
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';

class ImagePreprocessingService {
  
  /// 全面图像预处理流程
  static Future<Uint8List> preprocessImage(Uint8List imageBytes) async {
    if (kDebugMode) {
      print('🖼️ 开始图像预处理流程...');
    }

    try {
      // 1. 解码图像
      final ui.Codec codec = await ui.instantiateImageCodec(imageBytes);
      final ui.FrameInfo frameInfo = await codec.getNextFrame();
      final ui.Image originalImage = frameInfo.image;

      if (kDebugMode) {
        print('📐 原始图像尺寸: ${originalImage.width}x${originalImage.height}');
      }

      // 2. 转换为像素数据
      final ByteData? byteData = await originalImage.toByteData(format: ui.ImageByteFormat.rawRgba);
      if (byteData == null) {
        throw Exception('无法获取图像像素数据');
      }

      final Uint8List pixels = byteData.buffer.asUint8List();
      final int width = originalImage.width;
      final int height = originalImage.height;

      // 3. 应用预处理步骤
      Uint8List processedPixels = pixels;
      
      // 步骤1: 去模糊和去眩光
      processedPixels = _removeBlurAndGlare(processedPixels, width, height);
      
      // 步骤2: 透视校正（简化版）
      processedPixels = _perspectiveCorrection(processedPixels, width, height);
      
      // 步骤3: 转换为灰度图
      processedPixels = _convertToGrayscale(processedPixels, width, height);
      
      // 步骤4: 去噪处理
      processedPixels = _denoiseImage(processedPixels, width, height);
      
      // 步骤5: 增强对比度
      processedPixels = _enhanceContrast(processedPixels, width, height);
      
      // 步骤6: 锐化处理（针对小字体）
      processedPixels = _sharpenImage(processedPixels, width, height);
      
      // 步骤7: 二值化处理
      processedPixels = _binarizeImage(processedPixels, width, height);

      // 4. 转换回图像格式
      final ui.Image processedImage = await _createImageFromGrayscalePixels(
        processedPixels, width, height);
      
      final ByteData? processedByteData = await processedImage.toByteData(
        format: ui.ImageByteFormat.png);
      
      if (processedByteData == null) {
        throw Exception('无法生成处理后的图像数据');
      }

      if (kDebugMode) {
        print('✅ 图像预处理完成');
      }

      return processedByteData.buffer.asUint8List();

    } catch (e) {
      if (kDebugMode) {
        print('❌ 图像预处理失败: $e');
      }
      // 如果预处理失败，返回原始图像
      return imageBytes;
    }
  }

  /// 去模糊和去眩光处理
  static Uint8List _removeBlurAndGlare(Uint8List pixels, int width, int height) {
    if (kDebugMode) {
      print('🔧 应用去模糊和去眩光处理...');
    }

    final Uint8List result = Uint8List(pixels.length);
    
    for (int y = 1; y < height - 1; y++) {
      for (int x = 1; x < width - 1; x++) {
        final int index = (y * width + x) * 4;
        
        // 获取周围像素的平均值来减少眩光
        int sumR = 0, sumG = 0, sumB = 0;
        int count = 0;
        
        for (int dy = -1; dy <= 1; dy++) {
          for (int dx = -1; dx <= 1; dx++) {
            final int neighborIndex = ((y + dy) * width + (x + dx)) * 4;
            sumR += pixels[neighborIndex];
            sumG += pixels[neighborIndex + 1];
            sumB += pixels[neighborIndex + 2];
            count++;
          }
        }
        
        // 如果当前像素过亮（眩光），用周围像素平均值替代
        final int currentR = pixels[index];
        final int currentG = pixels[index + 1];
        final int currentB = pixels[index + 2];
        final int brightness = (currentR + currentG + currentB) ~/ 3;
        
        if (brightness > 240) { // 检测眩光
          result[index] = sumR ~/ count;
          result[index + 1] = sumG ~/ count;
          result[index + 2] = sumB ~/ count;
        } else {
          result[index] = currentR;
          result[index + 1] = currentG;
          result[index + 2] = currentB;
        }
        result[index + 3] = pixels[index + 3]; // Alpha通道
      }
    }
    
    return result;
  }

  /// 透视校正（简化版）
  static Uint8List _perspectiveCorrection(Uint8List pixels, int width, int height) {
    if (kDebugMode) {
      print('🔧 应用透视校正...');
    }
    
    // 简化的透视校正：主要是边缘检测和几何调整
    // 这里实现一个基础版本，实际项目中可能需要更复杂的算法
    return pixels; // 暂时返回原始像素，可以后续增强
  }

  /// 转换为灰度图
  static Uint8List _convertToGrayscale(Uint8List pixels, int width, int height) {
    if (kDebugMode) {
      print('🔧 转换为灰度图...');
    }

    final Uint8List grayscale = Uint8List(width * height);
    
    for (int i = 0; i < pixels.length; i += 4) {
      final int r = pixels[i];
      final int g = pixels[i + 1];
      final int b = pixels[i + 2];
      
      // 使用加权平均法转换为灰度
      final int gray = (0.299 * r + 0.587 * g + 0.114 * b).round();
      grayscale[i ~/ 4] = gray.clamp(0, 255);
    }
    
    return grayscale;
  }

  /// 去噪处理（中值滤波）
  static Uint8List _denoiseImage(Uint8List pixels, int width, int height) {
    if (kDebugMode) {
      print('🔧 应用去噪处理...');
    }

    final Uint8List result = Uint8List(pixels.length);
    
    for (int y = 1; y < height - 1; y++) {
      for (int x = 1; x < width - 1; x++) {
        final List<int> neighbors = [];
        
        // 收集3x3邻域的像素值
        for (int dy = -1; dy <= 1; dy++) {
          for (int dx = -1; dx <= 1; dx++) {
            final int index = (y + dy) * width + (x + dx);
            neighbors.add(pixels[index]);
          }
        }
        
        // 中值滤波
        neighbors.sort();
        final int median = neighbors[neighbors.length ~/ 2];
        
        final int index = y * width + x;
        result[index] = median;
      }
    }
    
    return result;
  }

  /// 增强对比度
  static Uint8List _enhanceContrast(Uint8List pixels, int width, int height) {
    if (kDebugMode) {
      print('🔧 增强对比度...');
    }

    // 计算直方图
    final List<int> histogram = List.filled(256, 0);
    for (int pixel in pixels) {
      histogram[pixel]++;
    }
    
    // 找到有效的最小和最大值（排除极值）
    int minVal = 0, maxVal = 255;
    final int totalPixels = pixels.length;
    final int threshold = (totalPixels * 0.01).round(); // 忽略1%的极值
    
    int count = 0;
    for (int i = 0; i < 256; i++) {
      count += histogram[i];
      if (count > threshold) {
        minVal = i;
        break;
      }
    }
    
    count = 0;
    for (int i = 255; i >= 0; i--) {
      count += histogram[i];
      if (count > threshold) {
        maxVal = i;
        break;
      }
    }
    
    // 线性拉伸对比度
    final Uint8List result = Uint8List(pixels.length);
    final double scale = 255.0 / (maxVal - minVal);
    
    for (int i = 0; i < pixels.length; i++) {
      final int newValue = ((pixels[i] - minVal) * scale).round();
      result[i] = newValue.clamp(0, 255);
    }
    
    return result;
  }

  /// 锐化处理（针对小字体）
  static Uint8List _sharpenImage(Uint8List pixels, int width, int height) {
    if (kDebugMode) {
      print('🔧 应用锐化处理...');
    }

    final Uint8List result = Uint8List(pixels.length);
    
    // 锐化卷积核
    final List<List<double>> kernel = [
      [0, -1, 0],
      [-1, 5, -1],
      [0, -1, 0]
    ];
    
    for (int y = 1; y < height - 1; y++) {
      for (int x = 1; x < width - 1; x++) {
        double sum = 0;
        
        for (int ky = 0; ky < 3; ky++) {
          for (int kx = 0; kx < 3; kx++) {
            final int pixelIndex = (y + ky - 1) * width + (x + kx - 1);
            sum += pixels[pixelIndex] * kernel[ky][kx];
          }
        }
        
        final int index = y * width + x;
        result[index] = sum.round().clamp(0, 255);
      }
    }
    
    return result;
  }

  /// 二值化处理
  static Uint8List _binarizeImage(Uint8List pixels, int width, int height) {
    if (kDebugMode) {
      print('🔧 应用二值化处理...');
    }

    // 使用Otsu算法计算最佳阈值
    final int threshold = _calculateOtsuThreshold(pixels);
    
    if (kDebugMode) {
      print('📊 二值化阈值: $threshold');
    }

    final Uint8List result = Uint8List(pixels.length);
    
    for (int i = 0; i < pixels.length; i++) {
      result[i] = pixels[i] > threshold ? 255 : 0;
    }
    
    return result;
  }

  /// 计算Otsu阈值
  static int _calculateOtsuThreshold(Uint8List pixels) {
    // 计算直方图
    final List<int> histogram = List.filled(256, 0);
    for (int pixel in pixels) {
      histogram[pixel]++;
    }
    
    final int total = pixels.length;
    double sum = 0;
    for (int i = 0; i < 256; i++) {
      sum += i * histogram[i];
    }
    
    double sumB = 0;
    int wB = 0;
    int wF = 0;
    double varMax = 0;
    int threshold = 0;
    
    for (int i = 0; i < 256; i++) {
      wB += histogram[i];
      if (wB == 0) continue;
      
      wF = total - wB;
      if (wF == 0) break;
      
      sumB += i * histogram[i];
      
      final double mB = sumB / wB;
      final double mF = (sum - sumB) / wF;
      
      final double varBetween = wB * wF * (mB - mF) * (mB - mF);
      
      if (varBetween > varMax) {
        varMax = varBetween;
        threshold = i;
      }
    }
    
    return threshold;
  }

  /// 从灰度像素创建图像
  static Future<ui.Image> _createImageFromGrayscalePixels(
      Uint8List grayscalePixels, int width, int height) async {
    
    // 将灰度像素转换为RGBA格式
    final Uint8List rgbaPixels = Uint8List(width * height * 4);
    
    for (int i = 0; i < grayscalePixels.length; i++) {
      final int gray = grayscalePixels[i];
      final int rgbaIndex = i * 4;
      
      rgbaPixels[rgbaIndex] = gray;     // R
      rgbaPixels[rgbaIndex + 1] = gray; // G
      rgbaPixels[rgbaIndex + 2] = gray; // B
      rgbaPixels[rgbaIndex + 3] = 255;  // A
    }
    
    final ui.Codec codec = await ui.instantiateImageCodec(
      rgbaPixels.buffer.asUint8List(),
      targetWidth: width,
      targetHeight: height,
    );
    
    final ui.FrameInfo frameInfo = await codec.getNextFrame();
    return frameInfo.image;
  }
}
