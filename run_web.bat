@echo off
echo === Azure OCR Web版启动 ===
echo.

REM 检查并创建.env文件
if not exist ".env" (
    if exist ".env.example" (
        echo Creating .env file from template...
        copy ".env.example" ".env" >nul
        echo ✓ .env file created from .env.example
        echo ⚠ Please edit .env file and add your Azure configuration
        echo.
    ) else (
        echo ✗ .env.example template not found
        echo.
    )
) else (
    echo ✓ .env file exists
)

echo 安装Web依赖...
flutter pub get

echo.
echo 启动Web服务器...
echo 应用将在浏览器中自动打开
echo 如果没有自动打开，请访问: http://localhost:3000
echo.
echo 按 Ctrl+C 停止服务器
echo.

flutter run -d web-server --web-port 3000
