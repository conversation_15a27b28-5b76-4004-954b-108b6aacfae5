import 'package:flutter/material.dart';

// 菜品数据模型
class Dish {
  final String id;
  final String name;
  final String category;
  final List<String> ingredients;
  final List<String> allergens;
  final String description;

  Dish({
    required this.id,
    required this.name,
    required this.category,
    required this.ingredients,
    required this.allergens,
    this.description = '',
  });

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'category': category,
      'ingredients': ingredients,
      'allergens': allergens,
      'description': description,
    };
  }

  factory Dish.fromJson(Map<String, dynamic> json) {
    return Dish(
      id: json['id'],
      name: json['name'],
      category: json['category'],
      ingredients: List<String>.from(json['ingredients']),
      allergens: List<String>.from(json['allergens']),
      description: json['description'] ?? '',
    );
  }
}

// 配料数据模型
class Ingredient {
  final String id;
  final String name;
  final String category;
  final List<String> allergens;
  final List<String> keywords;

  Ingredient({
    required this.id,
    required this.name,
    required this.category,
    required this.allergens,
    required this.keywords,
  });

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'category': category,
      'allergens': allergens,
      'keywords': keywords,
    };
  }

  factory Ingredient.fromJson(Map<String, dynamic> json) {
    return Ingredient(
      id: json['id'],
      name: json['name'],
      category: json['category'],
      allergens: List<String>.from(json['allergens']),
      keywords: List<String>.from(json['keywords']),
    );
  }
}

// 过敏原检测结果
class AllergenDetectionResult {
  final String dishName;
  final List<String> detectedIngredients;
  final List<String> detectedAllergens;
  final List<String> userAllergens;
  final List<String> matchedAllergens;
  final AllergenRiskLevel riskLevel;
  final String warning;
  final List<String> suggestions;

  AllergenDetectionResult({
    required this.dishName,
    required this.detectedIngredients,
    required this.detectedAllergens,
    required this.userAllergens,
    required this.matchedAllergens,
    required this.riskLevel,
    required this.warning,
    required this.suggestions,
  });

  bool get hasSafetyRisk => matchedAllergens.isNotEmpty;
  bool get isSafe => matchedAllergens.isEmpty;
}

// 过敏原风险等级
enum AllergenRiskLevel {
  safe, // 安全
  low, // 低风险
  medium, // 中风险
  high, // 高风险
  unknown, // 未知
}

extension AllergenRiskLevelExtension on AllergenRiskLevel {
  String get displayName {
    switch (this) {
      case AllergenRiskLevel.safe:
        return '安全';
      case AllergenRiskLevel.low:
        return '低风险';
      case AllergenRiskLevel.medium:
        return '中风险';
      case AllergenRiskLevel.high:
        return '高风险';
      case AllergenRiskLevel.unknown:
        return '未知';
    }
  }

  String get description {
    switch (this) {
      case AllergenRiskLevel.safe:
        return '未检测到过敏原，可以安全食用';
      case AllergenRiskLevel.low:
        return '检测到少量可能的过敏原，建议谨慎';
      case AllergenRiskLevel.medium:
        return '检测到明确的过敏原，不建议食用';
      case AllergenRiskLevel.high:
        return '检测到严重过敏原，强烈不建议食用';
      case AllergenRiskLevel.unknown:
        return '无法确定是否含有过敏原，建议询问服务员';
    }
  }

  Color get color {
    switch (this) {
      case AllergenRiskLevel.safe:
        return const Color(0xFF4CAF50); // 绿色
      case AllergenRiskLevel.low:
        return const Color(0xFFFF9800); // 橙色
      case AllergenRiskLevel.medium:
        return const Color(0xFFFF5722); // 深橙色
      case AllergenRiskLevel.high:
        return const Color(0xFFF44336); // 红色
      case AllergenRiskLevel.unknown:
        return const Color(0xFF9E9E9E); // 灰色
    }
  }
}
