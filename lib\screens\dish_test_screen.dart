import 'package:flutter/material.dart';
import '../services/ingredient_detection_service.dart';
import '../services/dish_database_service.dart';
import '../models/dish.dart';

// 菜品测试页面 - 用于测试配料数据库功能
class DishTestScreen extends StatefulWidget {
  const DishTestScreen({super.key});

  @override
  State<DishTestScreen> createState() => _DishTestScreenState();
}

class _DishTestScreenState extends State<DishTestScreen> {
  final TextEditingController _dishController = TextEditingController();
  AllergenDetectionResult? _result;
  bool _isAnalyzing = false;
  List<Dish> _sampleDishes = [];

  @override
  void initState() {
    super.initState();
    _loadSampleDishes();
  }

  void _loadSampleDishes() {
    DishDatabaseService.initialize();
    setState(() {
      _sampleDishes = DishDatabaseService.getAllDishes().take(8).toList();
    });
  }

  Future<void> _analyzeDish(String dishName) async {
    if (dishName.trim().isEmpty) return;

    setState(() {
      _isAnalyzing = true;
      _result = null;
    });

    try {
      final result = await IngredientDetectionService.detectAllergens(dishName);
      setState(() {
        _result = result;
        _isAnalyzing = false;
      });
    } catch (e) {
      setState(() {
        _isAnalyzing = false;
      });
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('分析失败: $e')),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('配料数据库测试'),
        backgroundColor: Colors.blue[600],
        foregroundColor: Colors.white,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 输入区域
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      '输入菜品名称进行分析:',
                      style:
                          TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                    ),
                    const SizedBox(height: 12),
                    TextField(
                      controller: _dishController,
                      decoration: const InputDecoration(
                        hintText: '例如: 宫保鸡丁、白灼虾、麻婆豆腐',
                        border: OutlineInputBorder(),
                      ),
                      onSubmitted: _analyzeDish,
                    ),
                    const SizedBox(height: 12),
                    SizedBox(
                      width: double.infinity,
                      child: ElevatedButton(
                        onPressed: _isAnalyzing
                            ? null
                            : () => _analyzeDish(_dishController.text),
                        child: _isAnalyzing
                            ? const Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  SizedBox(
                                    width: 16,
                                    height: 16,
                                    child: CircularProgressIndicator(
                                        strokeWidth: 2),
                                  ),
                                  SizedBox(width: 8),
                                  Text('分析中...'),
                                ],
                              )
                            : const Text('分析过敏原'),
                      ),
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 20),

            // 分析结果
            if (_result != null) _buildAnalysisResult(),

            const SizedBox(height: 20),

            // 示例菜品
            const Text(
              '数据库示例菜品:',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 12),

            if (_sampleDishes.isEmpty)
              const Center(child: CircularProgressIndicator())
            else
              GridView.builder(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                  crossAxisCount: 2,
                  childAspectRatio: 2.5,
                  crossAxisSpacing: 8,
                  mainAxisSpacing: 8,
                ),
                itemCount: _sampleDishes.length,
                itemBuilder: (context, index) {
                  final dish = _sampleDishes[index];
                  return Card(
                    child: InkWell(
                      onTap: () {
                        _dishController.text = dish.name;
                        _analyzeDish(dish.name);
                      },
                      child: Padding(
                        padding: const EdgeInsets.all(8.0),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              dish.name,
                              style: const TextStyle(
                                fontWeight: FontWeight.bold,
                                fontSize: 14,
                              ),
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                            ),
                            const SizedBox(height: 4),
                            Text(
                              dish.category,
                              style: TextStyle(
                                color: Colors.grey[600],
                                fontSize: 12,
                              ),
                            ),
                            const SizedBox(height: 4),
                            if (dish.allergens.isNotEmpty)
                              Text(
                                '含: ${dish.allergens.join(', ')}',
                                style: const TextStyle(
                                  color: Colors.red,
                                  fontSize: 11,
                                ),
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                              ),
                          ],
                        ),
                      ),
                    ),
                  );
                },
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildAnalysisResult() {
    final result = _result!;

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  result.isSafe ? Icons.check_circle : Icons.warning,
                  color: result.riskLevel.color,
                  size: 24,
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    '${result.dishName} - ${result.riskLevel.displayName}',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: result.riskLevel.color,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),

            // 警告信息
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: result.riskLevel.color.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                    color: result.riskLevel.color.withValues(alpha: 0.3)),
              ),
              child: Text(
                result.warning,
                style: TextStyle(
                  fontSize: 16,
                  color: result.riskLevel.color,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),

            if (result.detectedIngredients.isNotEmpty) ...[
              const SizedBox(height: 12),
              const Text(
                '检测到的配料:',
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 4),
              Wrap(
                spacing: 8,
                runSpacing: 4,
                children: result.detectedIngredients.map((ingredient) {
                  return Chip(
                    label: Text(ingredient),
                    backgroundColor: Colors.blue[50],
                  );
                }).toList(),
              ),
            ],

            if (result.matchedAllergens.isNotEmpty) ...[
              const SizedBox(height: 12),
              const Text(
                '匹配的过敏原:',
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 4),
              Wrap(
                spacing: 8,
                runSpacing: 4,
                children: result.matchedAllergens.map((allergen) {
                  return Chip(
                    label: Text(allergen),
                    backgroundColor: Colors.red[50],
                    labelStyle: const TextStyle(color: Colors.red),
                  );
                }).toList(),
              ),
            ],

            if (result.suggestions.isNotEmpty) ...[
              const SizedBox(height: 12),
              const Text(
                '建议:',
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 4),
              ...result.suggestions.map((suggestion) {
                return Padding(
                  padding: const EdgeInsets.only(bottom: 4),
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text('• ', style: TextStyle(fontSize: 16)),
                      Expanded(
                        child: Text(
                          suggestion,
                          style: const TextStyle(fontSize: 14),
                        ),
                      ),
                    ],
                  ),
                );
              }),
            ],
          ],
        ),
      ),
    );
  }
}
