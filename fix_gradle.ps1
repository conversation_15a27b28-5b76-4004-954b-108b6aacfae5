# 修复Gradle问题的PowerShell脚本
Write-Host "=== 修复Gradle问题 ===" -ForegroundColor Green
Write-Host ""

# 1. 清理Flutter缓存
Write-Host "1. 清理Flutter缓存..." -ForegroundColor Blue
try {
    flutter clean
    Write-Host "✓ Flutter缓存已清理" -ForegroundColor Green
} catch {
    Write-Host "✗ Flutter清理失败: $_" -ForegroundColor Red
}

Write-Host ""

# 2. 清理Gradle缓存
Write-Host "2. 清理Gradle缓存..." -ForegroundColor Blue
$gradleDir = "$env:USERPROFILE\.gradle"
if (Test-Path $gradleDir) {
    try {
        Remove-Item $gradleDir -Recurse -Force
        Write-Host "✓ Gradle缓存已清理" -ForegroundColor Green
    } catch {
        Write-Host "✗ 无法删除Gradle缓存: $_" -ForegroundColor Red
        Write-Host "请手动删除目录: $gradleDir" -ForegroundColor Yellow
    }
} else {
    Write-Host "✓ Gradle缓存目录不存在" -ForegroundColor Green
}

Write-Host ""

# 3. 清理Android构建缓存
Write-Host "3. 清理Android构建缓存..." -ForegroundColor Blue

$androidGradleDir = "android\.gradle"
if (Test-Path $androidGradleDir) {
    Remove-Item $androidGradleDir -Recurse -Force
    Write-Host "✓ Android Gradle缓存已清理" -ForegroundColor Green
}

$androidBuildDir = "android\app\build"
if (Test-Path $androidBuildDir) {
    Remove-Item $androidBuildDir -Recurse -Force
    Write-Host "✓ Android构建缓存已清理" -ForegroundColor Green
}

Write-Host ""

# 4. 重新获取依赖
Write-Host "4. 重新获取Flutter依赖..." -ForegroundColor Blue
try {
    flutter pub get
    Write-Host "✓ Flutter依赖获取成功" -ForegroundColor Green
} catch {
    Write-Host "✗ Flutter依赖获取失败: $_" -ForegroundColor Red
}

Write-Host ""

# 5. 尝试构建
Write-Host "5. 尝试构建APK..." -ForegroundColor Blue
Write-Host "这可能需要几分钟时间，请耐心等待..." -ForegroundColor Yellow

try {
    flutter build apk --debug
    Write-Host "✓ APK构建成功！" -ForegroundColor Green
} catch {
    Write-Host "✗ APK构建失败: $_" -ForegroundColor Red
    Write-Host "请检查网络连接或尝试使用VPN" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "=== 修复完成 ===" -ForegroundColor Green
Write-Host ""
Write-Host "现在可以尝试运行应用:" -ForegroundColor Yellow
Write-Host "flutter run" -ForegroundColor Cyan
Write-Host ""
Write-Host "如果仍有问题，请尝试:" -ForegroundColor Yellow
Write-Host "1. 检查网络连接" -ForegroundColor White
Write-Host "2. 使用VPN或更换网络" -ForegroundColor White
Write-Host "3. 重启Android Studio" -ForegroundColor White
Write-Host ""
Write-Host "按任意键继续..."
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
