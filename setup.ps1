# Azure OCR Flutter App Setup Script
Write-Host "=== Azure OCR Flutter App 设置脚本 ===" -ForegroundColor Green
Write-Host ""

# 检查Flutter是否存在
$flutterPath = "C:\flutter\bin\flutter.exe"
if (Test-Path $flutterPath) {
    Write-Host "✓ Flutter SDK 已找到: $flutterPath" -ForegroundColor Green
} else {
    Write-Host "✗ Flutter SDK 未找到在 C:\flutter" -ForegroundColor Red
    Write-Host "请确保Flutter已正确安装在 C:\flutter 目录" -ForegroundColor Yellow
    exit 1
}

# 检查.env文件
if (Test-Path ".env") {
    Write-Host "✓ .env 配置文件已存在" -ForegroundColor Green
    $envContent = Get-Content ".env"
    if ($envContent -match "your_azure_vision_endpoint_here" -or $envContent -match "your_azure_vision_key_here") {
        Write-Host "⚠ 请配置 .env 文件中的Azure信息" -ForegroundColor Yellow
    } else {
        Write-Host "✓ Azure配置看起来已完成" -ForegroundColor Green
    }
} else {
    Write-Host "✗ .env 文件不存在，正在从模板创建..." -ForegroundColor Yellow
    if (Test-Path ".env.example") {
        Copy-Item ".env.example" ".env"
        Write-Host "✓ 已从 .env.example 创建 .env 文件" -ForegroundColor Green
        Write-Host "⚠ 请编辑 .env 文件并填入您的Azure配置信息" -ForegroundColor Yellow
    } else {
        Write-Host "✗ .env.example 模板文件也不存在" -ForegroundColor Red
    }
}

Write-Host ""
Write-Host "正在安装Flutter依赖..." -ForegroundColor Blue

# 运行flutter pub get
try {
    & $flutterPath pub get
    Write-Host "✓ Flutter依赖安装成功!" -ForegroundColor Green
} catch {
    Write-Host "✗ Flutter依赖安装失败: $_" -ForegroundColor Red
    exit 1
}

Write-Host ""
Write-Host "=== 设置完成 ===" -ForegroundColor Green
Write-Host ""
Write-Host "下一步操作:" -ForegroundColor Yellow
Write-Host "1. 配置 .env 文件中的Azure信息（如果还未配置）"
Write-Host "2. 连接Android设备或启动模拟器"
Write-Host "3. 运行应用: C:\flutter\bin\flutter run"
Write-Host ""
Write-Host "按任意键继续..."
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
