import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;

class LLMIngredientService {
  // 使用免费的Hugging Face Inference API
  static const String _baseUrl = 'https://api-inference.huggingface.co/models';
  static const String _model = 'microsoft/DialoGPT-medium'; // 免费模型
  
  // 备用免费API
  static const String _ollamaUrl = 'http://localhost:11434/api/generate'; // 本地Ollama
  
  /// 使用LLM分析菜品配料
  static Future<Map<String, dynamic>> analyzeIngredientsWithLLM(
    String dishName, 
    String language
  ) async {
    if (kDebugMode) {
      print('🤖 开始LLM配料分析: "$dishName" (语言: $language)');
    }

    try {
      // 1. 首先尝试免费的在线LLM
      final onlineResult = await _tryOnlineLLM(dishName, language);
      if (onlineResult['success']) {
        return onlineResult;
      }

      // 2. 如果在线失败，尝试本地Ollama
      final localResult = await _tryLocalLLM(dishName, language);
      if (localResult['success']) {
        return localResult;
      }

      // 3. 如果都失败，使用增强的硬编码逻辑
      return _fallbackToEnhancedHardcoded(dishName, language);

    } catch (e) {
      if (kDebugMode) {
        print('❌ LLM分析失败: $e');
      }
      return _fallbackToEnhancedHardcoded(dishName, language);
    }
  }

  /// 尝试在线免费LLM
  static Future<Map<String, dynamic>> _tryOnlineLLM(
    String dishName, 
    String language
  ) async {
    try {
      final prompt = _buildPrompt(dishName, language);
      
      // 尝试多个免费API
      final apis = [
        _tryHuggingFace,
        _tryGroqAPI,
        _tryTogetherAI,
      ];

      for (final api in apis) {
        try {
          final result = await api(prompt);
          if (result['success']) {
            return result;
          }
        } catch (e) {
          if (kDebugMode) {
            print('API尝试失败: $e');
          }
          continue;
        }
      }

      return {'success': false, 'error': '所有在线API都失败'};
    } catch (e) {
      return {'success': false, 'error': e.toString()};
    }
  }

  /// 构建LLM提示词
  static String _buildPrompt(String dishName, String language) {
    final Map<String, String> prompts = {
      'zh': '''
请分析菜品"$dishName"的主要配料和可能的过敏原。

要求：
1. 列出主要配料（5-10个）
2. 识别可能的过敏原
3. 返回JSON格式

示例输出：
{
  "ingredients": ["鸡肉", "花生", "辣椒", "大葱", "蒜"],
  "allergens": ["花生", "可能含有坚果"],
  "confidence": 0.85,
  "language": "zh"
}

请分析：$dishName
''',
      'en': '''
Please analyze the main ingredients and potential allergens in the dish "$dishName".

Requirements:
1. List main ingredients (5-10 items)
2. Identify potential allergens
3. Return JSON format

Example output:
{
  "ingredients": ["chicken", "peanuts", "chili", "scallions", "garlic"],
  "allergens": ["peanuts", "may contain nuts"],
  "confidence": 0.85,
  "language": "en"
}

Please analyze: $dishName
''',
      'de': '''
Bitte analysieren Sie die Hauptzutaten und möglichen Allergene im Gericht "$dishName".

Anforderungen:
1. Hauptzutaten auflisten (5-10 Artikel)
2. Mögliche Allergene identifizieren
3. JSON-Format zurückgeben

Beispielausgabe:
{
  "ingredients": ["hähnchen", "erdnüsse", "chili", "frühlingszwiebeln", "knoblauch"],
  "allergens": ["erdnüsse", "kann nüsse enthalten"],
  "confidence": 0.85,
  "language": "de"
}

Bitte analysieren: $dishName
'''
    };

    return prompts[language] ?? prompts['en']!;
  }

  /// 尝试Hugging Face API
  static Future<Map<String, dynamic>> _tryHuggingFace(String prompt) async {
    try {
      final response = await http.post(
        Uri.parse('$_baseUrl/microsoft/DialoGPT-medium'),
        headers: {
          'Content-Type': 'application/json',
          // 注意：实际使用时需要申请免费的HF token
          // 'Authorization': 'Bearer YOUR_HF_TOKEN',
        },
        body: jsonEncode({
          'inputs': prompt,
          'parameters': {
            'max_new_tokens': 200,
            'temperature': 0.7,
          }
        }),
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        return _parseResponse(data, 'huggingface');
      }
      
      return {'success': false, 'error': 'HF API失败: ${response.statusCode}'};
    } catch (e) {
      return {'success': false, 'error': 'HF API异常: $e'};
    }
  }

  /// 尝试Groq API（免费）
  static Future<Map<String, dynamic>> _tryGroqAPI(String prompt) async {
    try {
      // Groq提供免费的LLM API
      final response = await http.post(
        Uri.parse('https://api.groq.com/openai/v1/chat/completions'),
        headers: {
          'Content-Type': 'application/json',
          // 'Authorization': 'Bearer YOUR_GROQ_API_KEY', // 免费申请
        },
        body: jsonEncode({
          'model': 'llama3-8b-8192',
          'messages': [
            {'role': 'user', 'content': prompt}
          ],
          'max_tokens': 200,
          'temperature': 0.7,
        }),
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        return _parseResponse(data, 'groq');
      }
      
      return {'success': false, 'error': 'Groq API失败: ${response.statusCode}'};
    } catch (e) {
      return {'success': false, 'error': 'Groq API异常: $e'};
    }
  }

  /// 尝试Together AI（免费额度）
  static Future<Map<String, dynamic>> _tryTogetherAI(String prompt) async {
    try {
      final response = await http.post(
        Uri.parse('https://api.together.xyz/inference'),
        headers: {
          'Content-Type': 'application/json',
          // 'Authorization': 'Bearer YOUR_TOGETHER_API_KEY', // 免费额度
        },
        body: jsonEncode({
          'model': 'togethercomputer/llama-2-7b-chat',
          'prompt': prompt,
          'max_tokens': 200,
          'temperature': 0.7,
        }),
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        return _parseResponse(data, 'together');
      }
      
      return {'success': false, 'error': 'Together API失败: ${response.statusCode}'};
    } catch (e) {
      return {'success': false, 'error': 'Together API异常: $e'};
    }
  }

  /// 尝试本地Ollama
  static Future<Map<String, dynamic>> _tryLocalLLM(
    String dishName, 
    String language
  ) async {
    try {
      final prompt = _buildPrompt(dishName, language);
      
      final response = await http.post(
        Uri.parse(_ollamaUrl),
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode({
          'model': 'llama3.2', // 或其他本地模型
          'prompt': prompt,
          'stream': false,
        }),
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        return _parseResponse(data, 'ollama');
      }
      
      return {'success': false, 'error': 'Ollama不可用'};
    } catch (e) {
      return {'success': false, 'error': 'Ollama连接失败: $e'};
    }
  }

  /// 解析LLM响应
  static Map<String, dynamic> _parseResponse(
    Map<String, dynamic> data, 
    String source
  ) {
    try {
      String responseText = '';
      
      switch (source) {
        case 'huggingface':
          responseText = data[0]['generated_text'] ?? '';
          break;
        case 'groq':
          responseText = data['choices'][0]['message']['content'] ?? '';
          break;
        case 'together':
          responseText = data['output']['text'] ?? '';
          break;
        case 'ollama':
          responseText = data['response'] ?? '';
          break;
      }

      // 尝试提取JSON
      final jsonMatch = RegExp(r'\{.*\}', dotAll: true).firstMatch(responseText);
      if (jsonMatch != null) {
        final jsonStr = jsonMatch.group(0)!;
        final parsed = jsonDecode(jsonStr);
        
        return {
          'success': true,
          'source': source,
          'ingredients': parsed['ingredients'] ?? [],
          'allergens': parsed['allergens'] ?? [],
          'confidence': parsed['confidence'] ?? 0.7,
          'language': parsed['language'] ?? 'unknown',
        };
      }

      // 如果没有JSON，尝试简单解析
      return _simpleParseResponse(responseText, source);
      
    } catch (e) {
      if (kDebugMode) {
        print('解析响应失败: $e');
      }
      return {'success': false, 'error': '解析失败: $e'};
    }
  }

  /// 简单解析响应
  static Map<String, dynamic> _simpleParseResponse(
    String text, 
    String source
  ) {
    final ingredients = <String>[];
    final allergens = <String>[];

    // 简单的关键词提取
    final lines = text.split('\n');
    for (final line in lines) {
      if (line.toLowerCase().contains('ingredient')) {
        // 提取配料
        final words = line.split(RegExp(r'[,，、]'));
        for (final word in words) {
          final clean = word.trim().replaceAll(RegExp(r'[^\w\s]'), '');
          if (clean.length > 2 && clean.length < 20) {
            ingredients.add(clean);
          }
        }
      }
    }

    return {
      'success': true,
      'source': source,
      'ingredients': ingredients.take(8).toList(),
      'allergens': allergens,
      'confidence': 0.6,
      'language': 'unknown',
    };
  }

  /// 增强的硬编码回退
  static Map<String, dynamic> _fallbackToEnhancedHardcoded(
    String dishName, 
    String language
  ) {
    if (kDebugMode) {
      print('🔄 回退到增强硬编码分析');
    }

    final ingredients = <String>[];
    final allergens = <String>[];

    // 基于菜名的智能推断
    final dishLower = dishName.toLowerCase();

    // 多语言配料推断规则
    final ingredientRules = {
      'zh': {
        '宫保鸡丁': ['鸡肉', '花生', '辣椒', '大葱', '蒜', '生抽', '料酒'],
        '麻婆豆腐': ['豆腐', '猪肉末', '豆瓣酱', '花椒', '大葱', '蒜'],
        '红烧肉': ['猪肉', '生抽', '老抽', '冰糖', '料酒', '大葱', '姜'],
      },
      'en': {
        'chicken': ['chicken', 'salt', 'pepper', 'oil'],
        'burger': ['beef patty', 'bun', 'lettuce', 'tomato', 'cheese'],
        'pizza': ['dough', 'tomato sauce', 'cheese', 'toppings'],
        'salad': ['lettuce', 'vegetables', 'dressing'],
      },
      'de': {
        'schnitzel': ['schweinefleisch', 'paniermehl', 'ei', 'mehl'],
        'bratwurst': ['schweinefleisch', 'gewürze', 'darm'],
        'sauerbraten': ['rindfleisch', 'essig', 'gewürze'],
      }
    };

    // 应用规则
    final rules = ingredientRules[language] ?? {};
    for (final entry in rules.entries) {
      if (dishLower.contains(entry.key.toLowerCase())) {
        ingredients.addAll(entry.value);
        break;
      }
    }

    // 如果没有匹配，使用通用推断
    if (ingredients.isEmpty) {
      ingredients.addAll(_genericIngredientInference(dishName, language));
    }

    return {
      'success': true,
      'source': 'enhanced_hardcoded',
      'ingredients': ingredients.take(8).toList(),
      'allergens': allergens,
      'confidence': 0.5,
      'language': language,
    };
  }

  /// 通用配料推断
  static List<String> _genericIngredientInference(String dishName, String language) {
    final dishLower = dishName.toLowerCase();
    final ingredients = <String>[];

    // 基础配料推断
    final commonIngredients = {
      'zh': ['盐', '油', '生抽', '蒜', '姜'],
      'en': ['salt', 'oil', 'pepper', 'garlic', 'onion'],
      'de': ['salz', 'öl', 'pfeffer', 'knoblauch', 'zwiebel'],
    };

    ingredients.addAll(commonIngredients[language] ?? commonIngredients['en']!);
    return ingredients;
  }
}
