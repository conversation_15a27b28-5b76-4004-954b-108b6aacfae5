# Azure OCR Flutter App

这是一个使用Azure Computer Vision API进行文字识别的Flutter应用，支持Android和iOS平台。

## 功能特性

### 📱 移动版 (Android/iOS)
- 📷 相机拍照
- 🖼️ 从相册选择图片
- 🔍 Azure OCR文字识别
- 🎨 现代化Material Design界面

### 🌐 Web版 (浏览器)
- 📁 文件上传选择图片
- 🔍 Azure OCR文字识别
- 💻 跨平台浏览器支持
- 📋 一键复制识别结果
- 🔗 实时Azure连接状态显示

## 配置说明

### 1. Azure Computer Vision API配置

1. 复制环境变量模板文件：
```bash
cp .env.example .env
```

2. 编辑 `.env` 文件，填入您的Azure服务信息：
```env
AZURE_VISION_ENDPOINT=https://your-resource-name.cognitiveservices.azure.com/
AZURE_VISION_KEY=your-subscription-key-here
```

⚠️ **重要**: `.env` 文件包含敏感信息，已添加到 `.gitignore` 中，不会被提交到版本控制系统。

### 2. 获取Azure配置信息

1. 登录[Azure门户](https://portal.azure.com/)
2. 创建或选择一个Computer Vision资源
3. 在"密钥和终结点"页面获取：
   - 终结点URL（AZURE_VISION_ENDPOINT）
   - 密钥（AZURE_VISION_KEY）

## 安装和运行

### 前提条件

- Flutter SDK (3.0.0+)
- Android Studio / Xcode
- Azure Computer Vision API账户

### 安装步骤

1. 克隆项目：
```bash
git clone <repository-url>
cd azure_ocr_app
```

2. 安装依赖：
```bash
flutter pub get
```

3. 配置Azure信息（见上面的配置说明）

4. 运行应用：

**移动版 (Android/iOS)**:
```bash
# Android
flutter run

# iOS
flutter run -d ios
```

**Web版 (浏览器)**:
```bash
# 使用批处理脚本
run_web.bat

# 或PowerShell脚本
powershell -ExecutionPolicy Bypass -File run_web.ps1

# 或手动启动
flutter run -d web-server --web-port 8080
```

🌐 **Web版详细指南**: 请查看 [WEB_GUIDE.md](WEB_GUIDE.md)

## 权限说明

### Android权限
- `CAMERA` - 相机拍照
- `READ_EXTERNAL_STORAGE` - 读取相册
- `INTERNET` - 网络请求

### iOS权限
- `NSCameraUsageDescription` - 相机使用说明
- `NSPhotoLibraryUsageDescription` - 相册访问说明

## 使用方法

1. 启动应用
2. 点击"拍照"按钮使用相机拍摄包含文字的图片
3. 或点击"相册"按钮从相册选择图片
4. 点击"开始文字识别"按钮
5. 等待识别完成，查看识别结果

## 技术栈

- **Flutter** - 跨平台移动应用框架
- **Azure Computer Vision API** - 微软云端OCR服务
- **image_picker** - 图片选择插件
- **permission_handler** - 权限管理插件
- **http** - HTTP请求库

## 注意事项

1. 确保设备有网络连接
2. 首次使用需要授予相机和存储权限
3. Azure API有使用限制，请注意配额
4. 建议使用清晰、光线充足的图片以获得更好的识别效果

## 故障排除

### Gradle构建问题
如果遇到Gradle下载超时或构建失败：

```cmd
# 使用自动修复脚本
fix_gradle.bat

# 或PowerShell版本
powershell -ExecutionPolicy Bypass -File fix_gradle.ps1
```

### 常见问题

1. **"Azure配置信息缺失"错误**
   - 检查`.env`文件是否存在且配置正确
   - 确认Azure终结点和密钥格式正确

2. **权限被拒绝**
   - 在设备设置中手动授予应用相机和存储权限

3. **网络请求失败**
   - 检查网络连接
   - 验证Azure服务状态
   - 确认API密钥有效

📖 **详细故障排除指南**: 请查看 [TROUBLESHOOTING.md](TROUBLESHOOTING.md)

## 许可证

MIT License
