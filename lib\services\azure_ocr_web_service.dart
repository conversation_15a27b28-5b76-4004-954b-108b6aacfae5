import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:flutter/foundation.dart';

class AzureOcrWebService {
  // 生产环境配置
  static String get _endpoint {
    // 在发布版本中直接使用配置值，开发环境使用.env文件
    if (kReleaseMode || kIsWeb) {
      return 'https://myocrservice1.cognitiveservices.azure.com/';
    }
    return dotenv.env['AZURE_VISION_ENDPOINT'] ??
        'https://myocrservice1.cognitiveservices.azure.com/';
  }

  static String get _apiKey {
    // 在发布版本中直接使用配置值，开发环境使用.env文件
    if (kReleaseMode || kIsWeb) {
      return '3TlS0kR0frzxGm4ePiYgLsnj07NcPt2rZvTEH40PTJWBQkWw1iFNJQQJ99BFACPV0roXJ3w3AAAFACOGPCXc';
    }
    return dotenv.env['AZURE_VISION_KEY'] ??
        '3TlS0kR0frzxGm4ePiYgLsnj07NcPt2rZvTEH40PTJWBQkWw1iFNJQQJ99BFACPV0roXJ3w3AAAFACOGPCXc';
  }

  static Future<String> extractTextFromImageBytes(Uint8List imageBytes) async {
    if (_endpoint.isEmpty || _apiKey.isEmpty) {
      throw Exception('Azure配置信息缺失，请检查.env文件');
    }

    try {
      // 第一步：提交图片进行分析
      final analyzeUri = Uri.parse('${_endpoint}vision/v3.2/read/analyze');

      if (kDebugMode) {
        print('发送Read分析请求到: $analyzeUri');
        print('图片大小: ${imageBytes.length} bytes');
      }

      final analyzeResponse = await http.post(
        analyzeUri,
        headers: {
          'Ocp-Apim-Subscription-Key': _apiKey,
          'Content-Type': 'application/octet-stream',
        },
        body: imageBytes,
      );

      if (kDebugMode) {
        print('分析响应状态码: ${analyzeResponse.statusCode}');
        print('分析响应头: ${analyzeResponse.headers}');
      }

      if (analyzeResponse.statusCode != 202) {
        throw Exception(
            'Read分析请求失败: ${analyzeResponse.statusCode} - ${analyzeResponse.body}');
      }

      // 获取操作位置URL
      final operationLocation = analyzeResponse.headers['operation-location'];
      if (operationLocation == null) {
        throw Exception('未找到操作位置URL');
      }

      if (kDebugMode) {
        print('操作位置: $operationLocation');
      }

      // 第二步：轮询结果
      return await _pollForResults(operationLocation);
    } catch (e) {
      if (kDebugMode) {
        print('OCR处理错误: $e');
      }
      throw Exception('OCR处理错误: $e');
    }
  }

  static Future<String> _pollForResults(String operationUrl) async {
    const maxAttempts = 30; // 最多尝试30次
    const delaySeconds = 2; // 每次间隔2秒

    for (int attempt = 0; attempt < maxAttempts; attempt++) {
      if (kDebugMode) {
        print('轮询结果，尝试 ${attempt + 1}/$maxAttempts');
      }

      final response = await http.get(
        Uri.parse(operationUrl),
        headers: {
          'Ocp-Apim-Subscription-Key': _apiKey,
        },
      );

      if (response.statusCode != 200) {
        throw Exception('获取结果失败: ${response.statusCode} - ${response.body}');
      }

      final jsonResponse = json.decode(response.body);
      final status = jsonResponse['status'];

      if (kDebugMode) {
        print('操作状态: $status');
      }

      if (status == 'succeeded') {
        return _parseReadResponse(jsonResponse);
      } else if (status == 'failed') {
        throw Exception('OCR处理失败');
      } else if (status == 'running' || status == 'notStarted') {
        // 继续等待
        await Future.delayed(const Duration(seconds: delaySeconds));
        continue;
      } else {
        throw Exception('未知状态: $status');
      }
    }

    throw Exception('OCR处理超时');
  }

  static String _parseReadResponse(Map<String, dynamic> response) {
    final StringBuffer result = StringBuffer();

    if (response['analyzeResult'] != null &&
        response['analyzeResult']['readResults'] != null) {
      for (var readResult in response['analyzeResult']['readResults']) {
        if (readResult['lines'] != null) {
          for (var line in readResult['lines']) {
            if (line['text'] != null) {
              result.writeln(line['text']);
            }
          }
        }
      }
    }

    final extractedText = result.toString().trim();
    return extractedText.isEmpty ? '未识别到文字内容' : extractedText;
  }

  // 测试Azure连接
  static Future<bool> testConnection() async {
    if (_endpoint.isEmpty || _apiKey.isEmpty) {
      if (kDebugMode) {
        print('Azure配置检查失败:');
        print('Endpoint: ${_endpoint.isEmpty ? "空" : "已设置"}');
        print('API Key: ${_apiKey.isEmpty ? "空" : "已设置 (${_apiKey.length}位)"}');
      }
      return false;
    }

    if (kDebugMode) {
      print('Azure配置检查:');
      print('Endpoint: $_endpoint');
      print('API Key长度: ${_apiKey.length}位');
    }

    try {
      // 简单的连接测试 - 使用GET请求测试终结点可达性
      final testUri = Uri.parse('${_endpoint}vision/v3.2/read/analyze');

      if (kDebugMode) {
        print('测试连接URL: $testUri');
      }

      // 对于Read API，我们只能通过实际的POST请求来测试
      // 但我们可以发送一个空的请求体来测试端点和认证
      final response = await http.post(
        testUri,
        headers: {
          'Ocp-Apim-Subscription-Key': _apiKey,
          'Content-Type': 'application/json',
        },
        body: '{"url":"https://example.com/test.jpg"}',
      );

      if (kDebugMode) {
        print('测试连接响应: ${response.statusCode}');
        if (response.statusCode != 202 && response.statusCode != 400) {
          print('错误响应: ${response.body}');
        }
      }

      // 202 (Accepted) 表示请求被接受（正常情况）
      // 400 (Bad Request) 可能是因为测试URL无效，但说明端点和认证正常
      return response.statusCode == 202 || response.statusCode == 400;
    } catch (e) {
      if (kDebugMode) {
        print('连接测试失败: $e');
      }
      return false;
    }
  }
}
