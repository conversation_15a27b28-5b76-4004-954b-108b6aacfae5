import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';

class LanguageService {
  static const String _languageKey = 'selected_language';
  
  // 支持的语言列表
  static const List<Locale> supportedLocales = [
    Locale('en', ''), // English
    Locale('zh', ''), // Chinese
    Locale('de', ''), // German
  ];
  
  // 获取保存的语言设置
  static Future<Locale> getSavedLanguage() async {
    final prefs = await SharedPreferences.getInstance();
    final languageCode = prefs.getString(_languageKey);
    
    if (languageCode != null) {
      return Locale(languageCode);
    }
    
    // 默认返回英语
    return const Locale('en');
  }
  
  // 保存语言设置
  static Future<void> saveLanguage(Locale locale) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(_languageKey, locale.languageCode);
  }
  
  // 获取语言显示名称
  static String getLanguageName(String languageCode) {
    switch (languageCode) {
      case 'en':
        return 'English';
      case 'zh':
        return '中文';
      case 'de':
        return 'Deutsch';
      default:
        return 'English';
    }
  }
  
  // 检测系统语言并返回支持的语言
  static Locale getSystemLanguage() {
    final systemLocale = WidgetsBinding.instance.platformDispatcher.locale;
    
    // 检查系统语言是否在支持列表中
    for (final supportedLocale in supportedLocales) {
      if (supportedLocale.languageCode == systemLocale.languageCode) {
        return supportedLocale;
      }
    }
    
    // 如果系统语言不支持，返回英语
    return const Locale('en');
  }
}
