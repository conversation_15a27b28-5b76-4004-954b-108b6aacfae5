// 过敏原数据模型
class Allergen {
  final String id;
  final String name;
  final String category;
  final String description;
  final bool isCommon;
  bool isSelected;

  Allergen({
    required this.id,
    required this.name,
    required this.category,
    required this.description,
    this.isCommon = false,
    this.isSelected = false,
  });

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'category': category,
      'description': description,
      'isCommon': isCommon,
      'isSelected': isSelected,
    };
  }

  factory Allergen.fromJson(Map<String, dynamic> json) {
    return Allergen(
      id: json['id'],
      name: json['name'],
      category: json['category'],
      description: json['description'],
      isCommon: json['isCommon'] ?? false,
      isSelected: json['isSelected'] ?? false,
    );
  }

  Allergen copyWith({
    String? id,
    String? name,
    String? category,
    String? description,
    bool? isCommon,
    bool? isSelected,
  }) {
    return Allergen(
      id: id ?? this.id,
      name: name ?? this.name,
      category: category ?? this.category,
      description: description ?? this.description,
      isCommon: isCommon ?? this.isCommon,
      isSelected: isSelected ?? this.isSelected,
    );
  }
}

// 预设的常见过敏原
class CommonAllergens {
  static List<Allergen> getCommonAllergens() {
    return [
      // 海鲜类
      Allergen(
        id: 'seafood_shrimp',
        name: '虾',
        category: '海鲜类',
        description: '虾类及虾制品',
        isCommon: true,
      ),
      Allergen(
        id: 'seafood_crab',
        name: '蟹',
        category: '海鲜类',
        description: '螃蟹及蟹制品',
        isCommon: true,
      ),
      Allergen(
        id: 'seafood_fish',
        name: '鱼',
        category: '海鲜类',
        description: '各种鱼类',
        isCommon: true,
      ),
      
      // 坚果类
      Allergen(
        id: 'nuts_peanut',
        name: '花生',
        category: '坚果类',
        description: '花生及花生制品',
        isCommon: true,
      ),
      Allergen(
        id: 'nuts_walnut',
        name: '核桃',
        category: '坚果类',
        description: '核桃及核桃制品',
        isCommon: true,
      ),
      Allergen(
        id: 'nuts_almond',
        name: '杏仁',
        category: '坚果类',
        description: '杏仁及杏仁制品',
        isCommon: true,
      ),
      
      // 乳制品
      Allergen(
        id: 'dairy_milk',
        name: '牛奶',
        category: '乳制品',
        description: '牛奶及乳制品',
        isCommon: true,
      ),
      Allergen(
        id: 'dairy_cheese',
        name: '奶酪',
        category: '乳制品',
        description: '各种奶酪制品',
        isCommon: true,
      ),
      
      // 蛋类
      Allergen(
        id: 'egg_chicken',
        name: '鸡蛋',
        category: '蛋类',
        description: '鸡蛋及蛋制品',
        isCommon: true,
      ),
      
      // 豆类
      Allergen(
        id: 'soy_soybean',
        name: '大豆',
        category: '豆类',
        description: '大豆及豆制品',
        isCommon: true,
      ),
      
      // 谷物类
      Allergen(
        id: 'grain_wheat',
        name: '小麦',
        category: '谷物类',
        description: '小麦及面制品',
        isCommon: true,
      ),
      
      // 其他
      Allergen(
        id: 'other_sesame',
        name: '芝麻',
        category: '其他',
        description: '芝麻及芝麻制品',
        isCommon: true,
      ),
      Allergen(
        id: 'other_msg',
        name: '味精',
        category: '其他',
        description: '味精及含MSG调料',
        isCommon: true,
      ),
    ];
  }
}
