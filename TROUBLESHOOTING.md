# 故障排除指南

## 🔧 Gradle构建问题

### 问题：Gradle下载超时
```
Exception in thread "main" java.lang.RuntimeException: Timeout of 120000 reached waiting for exclusive access to file
```

### 解决方案

#### 方法1：使用自动修复脚本
```cmd
# Windows批处理
fix_gradle.bat

# 或PowerShell
powershell -ExecutionPolicy Bypass -File fix_gradle.ps1
```

#### 方法2：手动修复步骤

1. **清理所有缓存**：
```cmd
flutter clean
rmdir /s /q "%USERPROFILE%\.gradle"
rmdir /s /q "android\.gradle"
rmdir /s /q "android\app\build"
```

2. **重新获取依赖**：
```cmd
flutter pub get
```

3. **尝试构建**：
```cmd
flutter build apk --debug
```

#### 方法3：网络问题解决

如果上述方法无效，可能是网络问题：

1. **更换网络**：
   - 尝试使用手机热点
   - 使用VPN服务

2. **配置代理**（如果有企业代理）：
   在 `android/gradle.properties` 中添加：
   ```properties
   systemProp.http.proxyHost=your-proxy-host
   systemProp.http.proxyPort=your-proxy-port
   systemProp.https.proxyHost=your-proxy-host
   systemProp.https.proxyPort=your-proxy-port
   ```

3. **使用国内镜像**：
   在 `android/build.gradle` 中替换仓库：
   ```gradle
   repositories {
       maven { url 'https://maven.aliyun.com/repository/google' }
       maven { url 'https://maven.aliyun.com/repository/central' }
       google()
       mavenCentral()
   }
   ```

## 🚀 Flutter运行问题

### 问题：设备未检测到
```cmd
flutter devices
```
如果没有显示设备：

1. **Android设备**：
   - 启用开发者选项
   - 启用USB调试
   - 安装ADB驱动

2. **模拟器**：
   - 启动Android Studio
   - 创建并启动AVD模拟器

### 问题：权限错误
如果应用运行时权限被拒绝：

1. **手动授权**：
   - 进入设备设置 > 应用 > azure_ocr_app > 权限
   - 启用相机和存储权限

2. **重新安装**：
```cmd
flutter clean
flutter run
```

## 🔐 Azure配置问题

### 问题：Azure配置信息缺失
```
Azure配置信息缺失，请检查.env文件
```

**解决方案**：
1. 确保 `.env` 文件存在
2. 检查配置格式：
```env
AZURE_VISION_ENDPOINT=https://your-resource.cognitiveservices.azure.com/
AZURE_VISION_KEY=your-api-key-here
```

### 问题：OCR请求失败
```
OCR请求失败: 401 - Unauthorized
```

**解决方案**：
1. 验证API密钥是否正确
2. 检查Azure资源是否激活
3. 确认终结点URL格式正确

## 📱 应用运行问题

### 问题：应用崩溃
1. **查看日志**：
```cmd
flutter logs
```

2. **调试模式运行**：
```cmd
flutter run --debug
```

3. **检查依赖**：
```cmd
flutter doctor
```

### 问题：图片选择失败
1. **检查权限**：确保已授予相机和存储权限
2. **重启应用**：完全关闭应用后重新打开
3. **检查设备兼容性**：确保设备支持相机功能

## 🛠️ 开发环境问题

### 问题：Flutter命令不识别
**解决方案**：
1. 检查Flutter是否正确安装在 `C:\flutter`
2. 使用完整路径：`C:\flutter\bin\flutter`
3. 添加到系统PATH环境变量

### 问题：Android SDK问题
**解决方案**：
1. 安装Android Studio
2. 运行 `flutter doctor` 检查SDK状态
3. 按照提示安装缺失的组件

## 📞 获取帮助

如果以上方法都无法解决问题：

1. **检查Flutter版本**：
```cmd
flutter --version
```

2. **运行诊断**：
```cmd
flutter doctor -v
```

3. **查看详细错误**：
```cmd
flutter run --verbose
```

4. **社区支持**：
   - Flutter官方文档：https://flutter.dev/docs
   - Stack Overflow：搜索相关错误信息
   - GitHub Issues：查看项目相关问题

## 🔄 重置项目

如果所有方法都失败，可以尝试完全重置：

```cmd
# 删除所有构建文件
flutter clean
rmdir /s /q "%USERPROFILE%\.gradle"
rmdir /s /q "android\.gradle"
rmdir /s /q "android\app\build"

# 重新获取依赖
flutter pub get

# 重新构建
flutter run
```
