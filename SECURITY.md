# 安全配置指南

## 🔐 环境变量安全

### 重要文件说明

- **`.env`** - 包含真实的API密钥和敏感配置，**绝不应该**提交到版本控制系统
- **`.env.example`** - 环境变量模板文件，可以安全地提交到Git
- **`.gitignore`** - 已配置忽略 `.env` 文件和其他敏感信息

### 配置步骤

1. **复制模板文件**：
   ```bash
   cp .env.example .env
   ```

2. **编辑 `.env` 文件**，填入真实的Azure配置：
   ```env
   AZURE_VISION_ENDPOINT=https://your-actual-resource.cognitiveservices.azure.com/
   AZURE_VISION_KEY=your-actual-api-key-here
   ```

3. **验证 `.gitignore`** 确保包含：
   ```
   .env
   .env.local
   .env.production
   .env.staging
   ```

## 🛡️ API密钥安全最佳实践

### Azure API密钥管理

1. **定期轮换密钥**
   - 在Azure门户中定期更新API密钥
   - 使用密钥1和密钥2进行无缝轮换

2. **限制访问权限**
   - 在Azure中配置IP白名单（如果可能）
   - 使用最小权限原则

3. **监控使用情况**
   - 定期检查Azure门户中的API使用情况
   - 设置使用量警报

### 开发环境安全

1. **本地开发**
   - 永远不要在代码中硬编码API密钥
   - 使用环境变量或配置文件
   - 确保 `.env` 文件不被提交

2. **团队协作**
   - 通过安全渠道分享API密钥（如密码管理器）
   - 不要通过聊天工具或邮件发送密钥
   - 每个开发者使用自己的Azure资源（推荐）

3. **生产部署**
   - 使用CI/CD系统的安全环境变量功能
   - 考虑使用Azure Key Vault等密钥管理服务
   - 为不同环境使用不同的API密钥

## 🚨 如果密钥泄露

如果您的API密钥意外泄露：

1. **立即行动**
   - 登录Azure门户
   - 重新生成受影响的API密钥
   - 更新所有使用该密钥的应用

2. **检查使用情况**
   - 查看Azure门户中的使用统计
   - 检查是否有异常的API调用

3. **更新配置**
   - 更新 `.env` 文件中的密钥
   - 重新部署应用（如果已部署）

## 📋 安全检查清单

在提交代码前，请确认：

- [ ] `.env` 文件未被添加到Git
- [ ] 代码中没有硬编码的API密钥
- [ ] `.gitignore` 包含所有敏感文件
- [ ] 使用 `.env.example` 作为配置模板
- [ ] API密钥具有适当的权限限制

## 🔍 检查命令

使用以下命令检查是否有敏感信息被意外提交：

```bash
# 检查Git历史中是否包含敏感信息
git log --all --full-history -- .env

# 检查当前暂存区
git status

# 检查是否有敏感文件被跟踪
git ls-files | grep -E "\.(env|key|secret)"
```

## 📞 报告安全问题

如果您发现安全漏洞，请通过以下方式报告：
- 不要在公开的Issue中报告安全问题
- 通过私有渠道联系项目维护者
- 提供详细的漏洞描述和复现步骤
