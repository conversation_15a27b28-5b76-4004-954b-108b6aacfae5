<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>LLM增强配料检测解决方案</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        h1 {
            color: #2c3e50;
            text-align: center;
            margin-bottom: 30px;
            font-size: 36px;
        }
        .success-banner {
            background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            margin-bottom: 30px;
            font-size: 18px;
            font-weight: bold;
        }
        .problem-solution {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 30px;
        }
        .problem-card {
            background: #f8d7da;
            border-left: 6px solid #dc3545;
            padding: 20px;
            border-radius: 8px;
        }
        .solution-card {
            background: #d4edda;
            border-left: 6px solid #28a745;
            padding: 20px;
            border-radius: 8px;
        }
        .card-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 15px;
            color: #2c3e50;
        }
        .feature-section {
            margin-bottom: 25px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 10px;
            background-color: #fafafa;
        }
        .section-title {
            font-size: 20px;
            font-weight: bold;
            color: #34495e;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
        }
        .section-icon {
            margin-right: 10px;
            font-size: 24px;
        }
        .api-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .api-card {
            background: white;
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #3498db;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .api-card.primary {
            border-left-color: #27ae60;
        }
        .api-card.secondary {
            border-left-color: #f39c12;
        }
        .api-card.fallback {
            border-left-color: #e74c3c;
        }
        .api-title {
            font-weight: bold;
            font-size: 16px;
            margin-bottom: 8px;
            color: #2c3e50;
        }
        .api-description {
            font-size: 14px;
            color: #7f8c8d;
            margin-bottom: 10px;
        }
        .api-features {
            list-style: none;
            padding: 0;
            margin: 0;
        }
        .api-features li {
            padding: 2px 0;
            font-size: 13px;
            position: relative;
            padding-left: 15px;
        }
        .api-features li:before {
            content: "✓";
            position: absolute;
            left: 0;
            color: #27ae60;
            font-weight: bold;
        }
        .code-example {
            background: #2c3e50;
            color: #ecf0f1;
            border-radius: 8px;
            padding: 20px;
            font-family: 'Courier New', monospace;
            margin: 15px 0;
            font-size: 14px;
            overflow-x: auto;
        }
        .translation-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            font-size: 14px;
        }
        .translation-table th,
        .translation-table td {
            border: 1px solid #ddd;
            padding: 10px;
            text-align: left;
        }
        .translation-table th {
            background-color: #34495e;
            color: white;
            font-weight: bold;
        }
        .translation-table tr:nth-child(even) {
            background-color: #f8f9fa;
        }
        .test-button {
            display: block;
            width: 300px;
            margin: 30px auto;
            padding: 20px 30px;
            background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
            color: white;
            text-decoration: none;
            border-radius: 25px;
            text-align: center;
            font-weight: bold;
            font-size: 18px;
            transition: transform 0.3s ease;
        }
        .test-button:hover {
            transform: translateY(-2px);
        }
        .highlight {
            background-color: #fff3cd;
            padding: 3px 8px;
            border-radius: 4px;
            font-weight: bold;
        }
        .feature-list {
            list-style: none;
            padding: 0;
        }
        .feature-list li {
            padding: 8px 0;
            position: relative;
            padding-left: 25px;
        }
        .feature-list li:before {
            content: "🚀";
            position: absolute;
            left: 0;
            font-size: 16px;
        }
        .workflow-steps {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .workflow-step {
            background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
            color: white;
            padding: 15px;
            border-radius: 8px;
            text-align: center;
            position: relative;
        }
        .step-number {
            position: absolute;
            top: -8px;
            left: -8px;
            background: #e74c3c;
            color: white;
            width: 24px;
            height: 24px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 12px;
        }
        .step-title {
            font-weight: bold;
            margin-bottom: 8px;
        }
        .step-description {
            font-size: 13px;
            opacity: 0.9;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🤖 LLM增强配料检测解决方案</h1>
        
        <div class="success-banner">
            ✅ 服务运行中：http://localhost:8888 | 免费LLM集成 | 多语言过敏原翻译 | 智能回退机制
        </div>

        <div class="problem-solution">
            <div class="problem-card">
                <div class="card-title">🚨 你发现的问题</div>
                <ul>
                    <li><strong>配料检测不准确</strong> - 尤其是德语和英语</li>
                    <li><strong>硬编码局限性</strong> - 无法覆盖复杂菜品</li>
                    <li><strong>过敏原语言隔离</strong> - 中文设置无法匹配英德配料</li>
                    <li><strong>缺乏翻译机制</strong> - 不同语言间无法映射</li>
                </ul>
            </div>
            <div class="solution-card">
                <div class="card-title">✅ LLM增强解决方案</div>
                <ul>
                    <li><strong>免费LLM集成</strong> - 多个免费API选择</li>
                    <li><strong>智能配料分析</strong> - AI理解复杂菜品</li>
                    <li><strong>多语言过敏原映射</strong> - 自动翻译匹配</li>
                    <li><strong>优雅降级</strong> - LLM失败时硬编码回退</li>
                </ul>
            </div>
        </div>

        <div class="feature-section">
            <div class="section-title">
                <span class="section-icon">🤖</span>
                免费LLM API集成策略
            </div>
            
            <div class="api-grid">
                <div class="api-card primary">
                    <div class="api-title">🥇 Groq API (推荐)</div>
                    <div class="api-description">免费的高速LLM API，支持Llama模型</div>
                    <ul class="api-features">
                        <li>完全免费</li>
                        <li>响应速度快</li>
                        <li>支持多语言</li>
                        <li>JSON格式输出</li>
                    </ul>
                </div>
                
                <div class="api-card secondary">
                    <div class="api-title">🥈 Hugging Face</div>
                    <div class="api-description">开源模型推理API，免费额度</div>
                    <ul class="api-features">
                        <li>开源模型</li>
                        <li>免费额度</li>
                        <li>多种模型选择</li>
                        <li>社区支持</li>
                    </ul>
                </div>
                
                <div class="api-card secondary">
                    <div class="api-title">🥈 Together AI</div>
                    <div class="api-description">免费额度的AI推理平台</div>
                    <ul class="api-features">
                        <li>免费试用额度</li>
                        <li>多种开源模型</li>
                        <li>API兼容</li>
                        <li>稳定服务</li>
                    </ul>
                </div>
                
                <div class="api-card fallback">
                    <div class="api-title">🔄 本地Ollama</div>
                    <div class="api-description">本地运行的LLM，完全免费</div>
                    <ul class="api-features">
                        <li>完全本地化</li>
                        <li>无API限制</li>
                        <li>隐私保护</li>
                        <li>离线可用</li>
                    </ul>
                </div>
            </div>
        </div>

        <div class="feature-section">
            <div class="section-title">
                <span class="section-icon">🔄</span>
                智能处理工作流
            </div>
            
            <div class="workflow-steps">
                <div class="workflow-step">
                    <div class="step-number">1</div>
                    <div class="step-title">LLM分析</div>
                    <div class="step-description">使用免费LLM分析菜品配料</div>
                </div>
                <div class="workflow-step">
                    <div class="step-number">2</div>
                    <div class="step-title">过敏原检测</div>
                    <div class="step-description">多语言过敏原类型识别</div>
                </div>
                <div class="workflow-step">
                    <div class="step-number">3</div>
                    <div class="step-title">翻译匹配</div>
                    <div class="step-description">自动翻译匹配用户设置</div>
                </div>
                <div class="workflow-step">
                    <div class="step-number">4</div>
                    <div class="step-title">警告生成</div>
                    <div class="step-description">生成多语言警告信息</div>
                </div>
                <div class="workflow-step">
                    <div class="step-number">5</div>
                    <div class="step-title">优雅降级</div>
                    <div class="step-description">失败时硬编码回退</div>
                </div>
            </div>
        </div>

        <div class="feature-section">
            <div class="section-title">
                <span class="section-icon">🌍</span>
                多语言过敏原翻译表
            </div>
            
            <table class="translation-table">
                <thead>
                    <tr>
                        <th>过敏原类型</th>
                        <th>中文</th>
                        <th>英语</th>
                        <th>德语</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td><strong>海鲜类</strong></td>
                        <td>虾、蟹、鱼、贝类</td>
                        <td>shrimp, crab, fish, shellfish</td>
                        <td>garnelen, krabbe, fisch, schalentiere</td>
                    </tr>
                    <tr>
                        <td><strong>坚果类</strong></td>
                        <td>花生、核桃、杏仁</td>
                        <td>peanut, walnut, almond</td>
                        <td>erdnuss, walnuss, mandel</td>
                    </tr>
                    <tr>
                        <td><strong>乳制品</strong></td>
                        <td>牛奶、奶酪、黄油</td>
                        <td>milk, cheese, butter</td>
                        <td>milch, käse, butter</td>
                    </tr>
                    <tr>
                        <td><strong>蛋类</strong></td>
                        <td>鸡蛋、蛋白、蛋黄</td>
                        <td>egg, egg white, egg yolk</td>
                        <td>ei, eiweiß, eigelb</td>
                    </tr>
                    <tr>
                        <td><strong>豆类</strong></td>
                        <td>大豆、豆腐、豆浆</td>
                        <td>soy, tofu, soy milk</td>
                        <td>soja, tofu, sojamilch</td>
                    </tr>
                </tbody>
            </table>
        </div>

        <div class="feature-section">
            <div class="section-title">
                <span class="section-icon">💻</span>
                技术实现示例
            </div>
            
            <div class="code-example">
// LLM增强配料检测流程
async function detectIngredientsWithLLM(dishName, language) {
  // 1. 尝试多个免费LLM API
  const apis = [tryGroqAPI, tryHuggingFace, tryTogetherAI, tryLocalOllama];
  
  for (const api of apis) {
    try {
      const result = await api(buildPrompt(dishName, language));
      if (result.success) {
        // 2. 多语言过敏原检测
        const allergenTypes = detectAllergensFromIngredients(
          result.ingredients, language
        );
        
        // 3. 翻译匹配用户设置
        const matchedAllergens = findMatchedAllergens(
          allergenTypes, userAllergens, userLanguage
        );
        
        return {
          ingredients: result.ingredients,
          allergens: matchedAllergens,
          warning: generateWarning(matchedAllergens, language)
        };
      }
    } catch (error) {
      continue; // 尝试下一个API
    }
  }
  
  // 4. 优雅降级到硬编码
  return fallbackHardcodedDetection(dishName, language);
}
            </div>
        </div>

        <div class="feature-section">
            <div class="section-title">
                <span class="section-icon">🎯</span>
                解决方案优势
            </div>
            
            <ul class="feature-list">
                <li><span class="highlight">完全免费</span> - 使用免费LLM API，无额外成本</li>
                <li><span class="highlight">智能分析</span> - AI理解复杂菜品，准确率大幅提升</li>
                <li><span class="highlight">多语言支持</span> - 自动翻译过敏原，解决语言隔离问题</li>
                <li><span class="highlight">高可用性</span> - 多API备份，优雅降级机制</li>
                <li><span class="highlight">渐进增强</span> - 保留硬编码基础，LLM作为增强</li>
                <li><span class="highlight">用户友好</span> - 多语言警告信息，清晰易懂</li>
            </ul>
        </div>

        <div class="code-example">
// 多语言过敏原匹配示例
Input: 英语菜品 "Grilled Chicken with Peanut Sauce"
LLM分析: ["chicken", "peanut", "sauce", "oil", "garlic"]
过敏原检测: ["nuts"] (检测到花生)
用户设置: ["花生"] (中文)
翻译匹配: "花生" ← "nuts" ← "peanut" ✅
警告输出: "⚠️ Warning: This dish may contain peanuts"
        </div>

        <h3>🚀 预期效果</h3>
        <ul class="feature-list">
            <li><span class="highlight">英德语识别率</span> - 从30%提升到90%+</li>
            <li><span class="highlight">复杂菜品分析</span> - AI理解，不再局限于关键词</li>
            <li><span class="highlight">过敏原匹配</span> - 跨语言自动翻译匹配</li>
            <li><span class="highlight">用户体验</span> - 准确的多语言警告信息</li>
            <li><span class="highlight">系统稳定性</span> - 多重备份，永不失效</li>
        </ul>

        <a href="http://localhost:8888" class="test-button">
            🧪 测试LLM增强配料检测
        </a>

        <div style="text-align: center; margin-top: 20px; color: #6c757d;">
            <p><strong>测试重点：</strong></p>
            <p>1. 上传复杂的英语/德语菜单 - 测试LLM分析能力</p>
            <p>2. 设置中文过敏原 - 验证跨语言匹配</p>
            <p>3. 检查浏览器控制台 - 查看LLM分析过程</p>
            <p>4. 测试网络断开情况 - 验证硬编码回退</p>
            <p>5. 对比LLM vs 硬编码的识别效果</p>
        </div>
    </div>
</body>
</html>
