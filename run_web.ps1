# Azure OCR Web版启动脚本
Write-Host "=== Azure OCR Web版启动 ===" -ForegroundColor Green
Write-Host ""

# 检查并创建.env文件
if (-not (Test-Path ".env")) {
    if (Test-Path ".env.example") {
        Write-Host "从模板创建.env文件..." -ForegroundColor Yellow
        Copy-Item ".env.example" ".env"
        Write-Host "✓ .env文件已从模板创建" -ForegroundColor Green
        Write-Host "⚠ 请编辑.env文件并添加您的Azure配置" -ForegroundColor Yellow
        Write-Host ""
    } else {
        Write-Host "✗ .env.example模板文件未找到" -ForegroundColor Red
        Write-Host ""
    }
} else {
    Write-Host "✓ .env文件已存在" -ForegroundColor Green
}

Write-Host "安装Web依赖..." -ForegroundColor Blue
try {
    flutter pub get
    Write-Host "✓ 依赖安装成功" -ForegroundColor Green
} catch {
    Write-Host "✗ 依赖安装失败: $_" -ForegroundColor Red
    exit 1
}

Write-Host ""
Write-Host "启动Web服务器..." -ForegroundColor Blue
Write-Host "应用将在浏览器中自动打开" -ForegroundColor Yellow
Write-Host "如果没有自动打开，请访问: http://localhost:3000" -ForegroundColor Cyan
Write-Host ""
Write-Host "按 Ctrl+C 停止服务器" -ForegroundColor Yellow
Write-Host ""

try {
    flutter run -d web-server --web-port 3000
} catch {
    Write-Host "✗ Web服务器启动失败: $_" -ForegroundColor Red
    Write-Host ""
    Write-Host "请尝试以下解决方案:" -ForegroundColor Yellow
    Write-Host "1. 确保端口8080未被占用" -ForegroundColor White
    Write-Host "2. 检查Flutter Web支持: flutter config --enable-web" -ForegroundColor White
    Write-Host "3. 重新安装依赖: flutter clean && flutter pub get" -ForegroundColor White
}
