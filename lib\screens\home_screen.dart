import 'dart:io';
import 'dart:typed_data';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:image_picker/image_picker.dart';
import 'package:permission_handler/permission_handler.dart';
import '../services/azure_ocr_service.dart';
import '../services/ingredient_detection_service.dart';
import '../services/allergen_service.dart';
import '../services/dish_database_service.dart';
import '../services/detection_history_service.dart';
import '../services/language_service.dart';
import '../models/dish.dart';
import '../models/detection_history.dart';
import 'allergen_settings_screen.dart';
import 'dish_test_screen.dart';
import 'detection_history_screen.dart';

class HomeScreen extends StatefulWidget {
  final Function(Locale)? onLanguageChanged;

  const HomeScreen({super.key, this.onLanguageChanged});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  XFile? _selectedXFile; // 统一使用XFile
  Uint8List? _imageBytes; // 图片字节数据
  String _extractedText = '';
  bool _isLoading = false;
  AllergenDetectionResult? _allergenResult;
  bool _isAnalyzingAllergens = false;
  String _debugInfo = ''; // 添加调试信息显示
  List<AllergenDetectionResult>? _batchResults; // 🆕 存储每道菜的详细检测结果
  final ImagePicker _picker = ImagePicker();

  @override
  void initState() {
    super.initState();
    // 初始化数据库
    DishDatabaseService.initialize();
    // 预加载用户过敏原设置
    AllergenService.getUserAllergens();
    // 🆕 预加载检测历史记录
    DetectionHistoryService.preloadHistory();
  }

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;

    return Scaffold(
      appBar: AppBar(
        title: Text(l10n.homeTitle),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
        actions: [
          // 语言切换按钮
          PopupMenuButton<Locale>(
            icon: const Icon(Icons.language),
            tooltip: l10n.selectLanguage,
            onSelected: (Locale locale) {
              if (widget.onLanguageChanged != null) {
                widget.onLanguageChanged!(locale);
              }
            },
            itemBuilder: (BuildContext context) {
              return LanguageService.supportedLocales.map((Locale locale) {
                return PopupMenuItem<Locale>(
                  value: locale,
                  child: Text(
                      LanguageService.getLanguageName(locale.languageCode)),
                );
              }).toList();
            },
          ),
          IconButton(
            onPressed: () {
              Navigator.of(context).push(
                MaterialPageRoute(
                  builder: (context) => const DetectionHistoryScreen(),
                ),
              );
            },
            icon: const Icon(Icons.history),
            tooltip: l10n.history,
          ),
          IconButton(
            onPressed: () {
              Navigator.of(context).push(
                MaterialPageRoute(
                  builder: (context) => const DishTestScreen(),
                ),
              );
            },
            icon: const Icon(Icons.science),
            tooltip: l10n.dishDatabase,
          ),
          IconButton(
            onPressed: () {
              Navigator.of(context).push(
                MaterialPageRoute(
                  builder: (context) => const AllergenSettingsScreen(),
                ),
              );
            },
            icon: const Icon(Icons.settings),
            tooltip: l10n.allergenSettings,
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // 图片显示区域
            Container(
              height: 300,
              decoration: BoxDecoration(
                border: Border.all(color: Colors.grey),
                borderRadius: BorderRadius.circular(8),
              ),
              child: _buildImageDisplay(),
            ),
            const SizedBox(height: 20),

            // 按钮区域
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: () => _pickImage(ImageSource.camera),
                    icon: const Icon(Icons.camera_alt),
                    label: Text(l10n.takePhoto),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: () => _pickImage(ImageSource.gallery),
                    icon: const Icon(Icons.photo_library),
                    label: Text(l10n.selectFromGallery),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),

            // OCR按钮
            ElevatedButton.icon(
              onPressed:
                  _selectedXFile != null && !_isLoading ? _performOcr : null,
              icon: _isLoading
                  ? const SizedBox(
                      width: 20,
                      height: 20,
                      child: CircularProgressIndicator(strokeWidth: 2),
                    )
                  : const Icon(Icons.text_fields),
              label: Text(_isLoading ? l10n.processing : l10n.startOCR),
              style: ElevatedButton.styleFrom(
                padding: const EdgeInsets.symmetric(vertical: 12),
              ),
            ),
            const SizedBox(height: 20),

            // 识别结果显示区域
            if (_extractedText.isNotEmpty) ...[
              const Text(
                '识别结果:',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 8),
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.grey[100],
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.grey[300]!),
                ),
                child: SelectableText(
                  _extractedText,
                  style: const TextStyle(fontSize: 16),
                ),
              ),
              const SizedBox(height: 20),

              // 过敏原分析结果
              _buildAllergenAnalysisSection(),

              // 调试信息显示（Web版本）
              if (_debugInfo.isNotEmpty) ...[
                const SizedBox(height: 16),
                Card(
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text(
                          '🔍 调试信息',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 8),
                        Container(
                          width: double.infinity,
                          padding: const EdgeInsets.all(12),
                          decoration: BoxDecoration(
                            color: Colors.grey[50],
                            borderRadius: BorderRadius.circular(8),
                            border: Border.all(color: Colors.grey[300]!),
                          ),
                          child: SelectableText(
                            _debugInfo,
                            style: const TextStyle(
                              fontSize: 12,
                              fontFamily: 'monospace',
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ],

              // 测试按钮（临时总是显示）
              // if (kDebugMode) ...[
              ...[
                const SizedBox(height: 16),
                Row(
                  children: [
                    Expanded(
                      child: ElevatedButton(
                        onPressed: _testGongBaoJiDing,
                        child: const Text('🧪 宫保鸡丁'),
                      ),
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: ElevatedButton(
                        onPressed: _testBaiZhuoXia,
                        child: const Text('🦐 白灼虾'),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                Row(
                  children: [
                    Expanded(
                      child: ElevatedButton(
                        onPressed: _debugPrintAllDishes,
                        style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.orange),
                        child: const Text('📚 查看数据库'),
                      ),
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: ElevatedButton(
                        onPressed: _testOcrMatching,
                        style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.purple),
                        child: const Text('🔍 测试匹配'),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                Row(
                  children: [
                    Expanded(
                      child: ElevatedButton(
                        onPressed: _testXiHongShiChaoJiDan,
                        child: const Text('🥚 西红柿炒鸡蛋'),
                      ),
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: ElevatedButton(
                        onPressed: _testMaPoDouFu,
                        child: const Text('🌶️ 麻婆豆腐'),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                Row(
                  children: [
                    Expanded(
                      child: ElevatedButton(
                        onPressed: _testIngredientDetection,
                        style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.green),
                        child: const Text('🔍 测试配料检测'),
                      ),
                    ),
                  ],
                ),
              ],
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildImageDisplay() {
    final l10n = AppLocalizations.of(context)!;

    if (_imageBytes != null) {
      return ClipRRect(
        borderRadius: BorderRadius.circular(8),
        child: Image.memory(
          _imageBytes!,
          fit: BoxFit.contain,
        ),
      );
    }

    // 默认显示
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(
            Icons.image,
            size: 64,
            color: Colors.grey,
          ),
          const SizedBox(height: 8),
          Text(
            l10n.noImageSelected,
            style: const TextStyle(
              color: Colors.grey,
              fontSize: 16,
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _pickImage(ImageSource source) async {
    try {
      // Web环境下不需要权限请求
      if (!kIsWeb) {
        // 移动端请求权限
        if (source == ImageSource.camera) {
          final cameraStatus = await Permission.camera.request();
          if (!cameraStatus.isGranted) {
            _showErrorDialog('需要相机权限才能拍照');
            return;
          }
        } else {
          final storageStatus = await Permission.photos.request();
          if (!storageStatus.isGranted) {
            _showErrorDialog('需要存储权限才能访问相册');
            return;
          }
        }
      }

      final XFile? image = await _picker.pickImage(
        source: source,
        maxWidth: 1920,
        maxHeight: 1080,
        imageQuality: 85,
      );

      if (image != null) {
        // 统一加载图片字节数据
        final bytes = await image.readAsBytes();
        setState(() {
          _selectedXFile = image;
          _imageBytes = bytes;
          _extractedText = ''; // 清空之前的识别结果
        });
      }
    } catch (e) {
      _showErrorDialog('选择图片失败: $e');
    }
  }

  Future<void> _performOcr() async {
    if (_selectedXFile == null) return;

    setState(() {
      _isLoading = true;
      _extractedText = '';
    });

    try {
      final extractedText =
          await AzureOcrService.extractTextFromXFile(_selectedXFile!);
      setState(() {
        _extractedText = extractedText.isEmpty ? '未识别到文字' : extractedText;
      });

      // 自动进行过敏原分析
      if (extractedText.isNotEmpty && extractedText != '未识别到文字') {
        await _analyzeAllergens(extractedText);
      }
    } catch (e) {
      _showErrorDialog('文字识别失败: $e');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  // 分析过敏原
  Future<void> _analyzeAllergens(String text) async {
    setState(() {
      _isAnalyzingAllergens = true;
      _allergenResult = null;
    });

    try {
      // 分析文本中的每一行，寻找菜品名称
      final lines =
          text.split('\n').where((line) => line.trim().isNotEmpty).toList();

      // 强制输出调试信息（Web版本）
      String debugText = '🏠 主页过敏原分析调试:\n';
      debugText += '  原始OCR文本: "$text"\n';
      debugText += '  分割后的行数: ${lines.length}\n';
      for (int i = 0; i < lines.length; i++) {
        debugText += '  第${i + 1}行: "${lines[i]}"\n';
      }

      setState(() {
        _debugInfo = debugText;
      });

      print('🏠 主页过敏原分析调试:');
      print('  原始OCR文本: "$text"');
      print('  分割后的行数: ${lines.length}');
      for (int i = 0; i < lines.length; i++) {
        print('  第${i + 1}行: "${lines[i]}"');
      }

      if (lines.isNotEmpty) {
        try {
          // 🆕 使用增强的多菜品检测功能
          print('🔍 开始调用 extractDishNames...');
          final extractedDishes =
              IngredientDetectionService.extractDishNames(lines);
          print('🔍 extractDishNames 调用完成，结果: $extractedDishes');

          // 更新调试信息，包含菜品提取结果
          String updatedDebugText = _debugInfo;
          updatedDebugText += '\n📋 菜品提取结果:\n';
          updatedDebugText += '  提取到的菜品数量: ${extractedDishes.length}\n';
          for (int i = 0; i < extractedDishes.length; i++) {
            updatedDebugText += '  菜品${i + 1}: "${extractedDishes[i]}"\n';
          }

          // 检查用户过敏原设置
          print('🔍 开始获取用户过敏原设置...');
          final userAllergens = AllergenService.getSelectedAllergensSync();
          print('🔍 用户过敏原设置获取完成: ${userAllergens.map((a) => a.name).toList()}');

          updatedDebugText += '\n🔧 用户过敏原设置:\n';
          updatedDebugText += '  用户过敏原数量: ${userAllergens.length}\n';
          if (userAllergens.isEmpty) {
            updatedDebugText += '  ⚠️ 用户未设置任何过敏原！\n';
            updatedDebugText += '  💡 建议：点击"过敏原设置"添加过敏原\n';
          } else {
            for (int i = 0; i < userAllergens.length; i++) {
              updatedDebugText += '  过敏原${i + 1}: ${userAllergens[i].name}\n';
            }
          }

          setState(() {
            _debugInfo = updatedDebugText;
          });
          print('🔍 调试信息更新完成');

          if (kDebugMode) {
            print('  📋 提取到的菜品: $extractedDishes');
            print('  当前用户过敏原设置: ${userAllergens.map((a) => a.name).toList()}');
            print('  用户过敏原数量: ${userAllergens.length}');
          }

          AllergenDetectionResult result;

          if (extractedDishes.isNotEmpty) {
            // 🆕 批量检测所有菜品的配料和过敏原
            if (kDebugMode) {
              print('  🎯 开始批量检测 ${extractedDishes.length} 道菜品...');
            }

            final batchResults =
                await IngredientDetectionService.batchDetectAllergens(
                    extractedDishes);

            if (kDebugMode) {
              print('  📊 批量检测完成，结果数量: ${batchResults.length}');
              for (int i = 0; i < batchResults.length; i++) {
                final r = batchResults[i];
                print('    菜品${i + 1}: ${r.dishName}');
                print('      配料: ${r.detectedIngredients}');
                print('      过敏原: ${r.detectedAllergens}');
                print('      匹配: ${r.matchedAllergens}');
                print('      风险: ${r.riskLevel.displayName}');
              }
            }

            // 🆕 保存批量检测的详细结果
            if (kDebugMode) {
              print('🔍 保存批量检测结果，数量: ${batchResults.length}');
              for (int i = 0; i < batchResults.length; i++) {
                final r = batchResults[i];
                print(
                    '  保存结果${i + 1}: ${r.dishName} - 配料: ${r.detectedIngredients}');
              }
            }
            _batchResults = batchResults;

            // 合并所有检测结果
            result = _mergeBatchResults(batchResults, extractedDishes);
          } else {
            // 如果没有提取到菜品，使用原来的智能选择逻辑
            final mainDish = _selectBestDishName(lines);

            if (kDebugMode) {
              print('  🔄 回退到智能选择: "$mainDish"');
            }

            result = await IngredientDetectionService.detectAllergens(mainDish);

            // 🆕 清空批量结果，因为这是单个菜品检测
            _batchResults = null;
          }

          if (kDebugMode) {
            print('  📊 过敏原检测结果:');
            print('    检测到的配料: ${result.detectedIngredients}');
            print('    检测到的过敏原: ${result.detectedAllergens}');
            print('    用户过敏原: ${result.userAllergens}');
            print('    匹配的过敏原: ${result.matchedAllergens}');
            print('    风险等级: ${result.riskLevel}');
            print('    警告信息: ${result.warning}');
          }

          setState(() {
            _allergenResult = result;
            _isAnalyzingAllergens = false;
          });

          // 🆕 保存检测历史记录
          await _saveDetectionHistory(
              text, extractedDishes, _batchResults ?? [], result);
        } catch (e) {
          print('🔍 菜品提取或过敏原分析出错: $e');
          setState(() {
            _debugInfo += '\n❌ 错误: $e';
            _isAnalyzingAllergens = false;
          });
          _showErrorDialog('过敏原分析失败: $e');
        }
      } else {
        setState(() {
          _isAnalyzingAllergens = false;
        });
      }
    } catch (e) {
      setState(() {
        _isAnalyzingAllergens = false;
      });
      _showErrorDialog('过敏原分析失败: $e');
    }
  }

  // 智能选择最佳菜品名称
  String _selectBestDishName(List<String> lines) {
    if (kDebugMode) {
      print('  开始智能菜品选择...');
      print('  总共${lines.length}行需要处理');

      // 先检查是否有包含"宫"的行
      for (int i = 0; i < lines.length; i++) {
        final line = lines[i].trim();
        if (line.contains('宫')) {
          print('  🔍 发现包含"宫"的行 第${i + 1}行: "$line"');
        }
      }
    }

    // 第一步：直接在所有行中查找数据库匹配的菜品
    for (int i = 0; i < lines.length; i++) {
      final line = lines[i].trim();

      // 跳过明显的价格行和数字行
      if (line.contains('元') ||
          line.contains('兀') ||
          RegExp(r'^\d+$').hasMatch(line)) {
        continue;
      }

      // 使用改进的文字清洗和匹配逻辑
      if (kDebugMode) {
        print('  检查第${i + 1}行: "$line"');
      }

      final matches = DishDatabaseService.searchDishes(line);
      if (matches.isNotEmpty) {
        if (kDebugMode) {
          print('  ✅ 找到数据库匹配: "$line" (第${i + 1}行) -> ${matches.first.name}');
        }
        return line;
      } else {
        if (kDebugMode && line.length > 2) {
          print('  ❌ 未匹配到数据库: "$line"');
        }
      }

      // 检查是否包含数据库菜品的关键词
      final allDishes = DishDatabaseService.getAllDishes();
      for (var dish in allDishes) {
        final dishName = dish.name.replaceAll(' ', '');
        final cleanLine = line.replaceAll(' ', '');
        if (cleanLine.contains(dishName) || dishName.contains(cleanLine)) {
          if (kDebugMode) {
            print('  找到数据库关键词匹配: "$line" -> "${dish.name}" (第${i + 1}行)');
          }
          return line;
        }

        // 特别检查宫保鸡丁
        if (kDebugMode &&
            (cleanLine.contains('宫') || dish.name.contains('宫'))) {
          print('    检查宫保鸡丁: "$cleanLine" vs "${dish.name}" ($dishName)');
        }
      }
    }

    // 特别处理：如果没有找到数据库匹配，直接查找包含"宫保鸡丁"的行
    if (kDebugMode) {
      print('  开始特别处理，查找宫保鸡丁...');
    }

    for (int i = 0; i < lines.length; i++) {
      final line = lines[i].trim();
      final cleanLine = line.replaceAll(' ', '');

      if (kDebugMode) {
        print('  检查第${i + 1}行特别处理: "$line" -> "$cleanLine"');
      }

      if (cleanLine.contains('宫保鸡丁') ||
          (cleanLine.contains('宫') &&
              cleanLine.contains('保') &&
              cleanLine.contains('鸡') &&
              cleanLine.contains('丁'))) {
        if (kDebugMode) {
          print('  ✅ 特别匹配到宫保鸡丁: "$line" (第${i + 1}行)');
        }
        return line;
      }
    }

    if (kDebugMode) {
      print('  特别处理未找到宫保鸡丁');
    }

    if (kDebugMode) {
      print('  未找到数据库匹配，使用过滤逻辑...');
    }

    // 第二步：如果没有数据库匹配，使用过滤逻辑
    final filteredLines = lines.where((line) {
      final cleanLine = line.trim();

      // 过滤掉价格行
      if (cleanLine.contains('元') || cleanLine.contains('兀')) {
        return false;
      }

      // 过滤掉数字行
      if (RegExp(r'^\d+$').hasMatch(cleanLine)) {
        return false;
      }

      // 过滤掉纯符号行
      if (RegExp(r'^[^\u4e00-\u9fa5a-zA-Z]+$').hasMatch(cleanLine)) {
        return false;
      }

      // 过滤掉明显的标题行
      if (cleanLine.contains('家常菜') && cleanLine.contains('荤菜')) {
        return false;
      }

      // 过滤掉太短的行
      if (cleanLine.length < 3) {
        return false;
      }

      return true;
    }).toList();

    if (kDebugMode) {
      print('  过滤后的候选菜品:');
      for (int i = 0; i < filteredLines.length && i < 10; i++) {
        print('    候选${i + 1}: "${filteredLines[i].trim()}"');
      }
    }

    if (filteredLines.isNotEmpty) {
      final selected = filteredLines.first.trim();
      if (kDebugMode) {
        print('  最终选择: "$selected"');
      }
      return selected;
    }

    // 如果没有合适的候选，返回第一行
    final fallback = lines.first.trim();
    if (kDebugMode) {
      print('  回退选择: "$fallback"');
    }
    return fallback;
  }

  // 构建过敏原分析结果区域
  Widget _buildAllergenAnalysisSection() {
    if (_isAnalyzingAllergens) {
      return const Card(
        child: Padding(
          padding: EdgeInsets.all(16.0),
          child: Column(
            children: [
              CircularProgressIndicator(),
              SizedBox(height: 8),
              Text('正在分析过敏原...'),
            ],
          ),
        ),
      );
    }

    if (_allergenResult == null) {
      return const SizedBox.shrink();
    }

    final result = _allergenResult!;

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  result.isSafe ? Icons.check_circle : Icons.warning,
                  color: result.riskLevel.color,
                  size: 24,
                ),
                const SizedBox(width: 8),
                Text(
                  '过敏原分析 - ${result.riskLevel.displayName}',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: result.riskLevel.color,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),

            // 警告信息
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: result.riskLevel.color.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
                border:
                    Border.all(color: result.riskLevel.color.withOpacity(0.3)),
              ),
              child: Text(
                result.warning,
                style: TextStyle(
                  fontSize: 16,
                  color: result.riskLevel.color,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),

            // 🆕 显示每道菜的详细配料检测结果
            if (_batchResults != null && _batchResults!.isNotEmpty) ...[
              const SizedBox(height: 12),
              const Text(
                '每道菜的配料检测结果:',
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 8),
              ..._batchResults!.asMap().entries.map((entry) {
                final index = entry.key;
                final dishResult = entry.value;
                return Container(
                  margin: const EdgeInsets.only(bottom: 12),
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    border: Border.all(color: Colors.grey[300]!),
                    borderRadius: BorderRadius.circular(8),
                    color: Colors.grey[50],
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // 菜品名称
                      Row(
                        children: [
                          Text(
                            '${index + 1}. ${dishResult.dishName}',
                            style: const TextStyle(
                              fontSize: 14,
                              fontWeight: FontWeight.bold,
                              color: Colors.black87,
                            ),
                          ),
                          const Spacer(),
                          // 风险等级标识
                          Container(
                            padding: const EdgeInsets.symmetric(
                                horizontal: 6, vertical: 2),
                            decoration: BoxDecoration(
                              color: _getRiskLevelBackgroundColor(
                                  dishResult.riskLevel),
                              borderRadius: BorderRadius.circular(4),
                              border: Border.all(
                                color: _getRiskLevelBorderColor(
                                    dishResult.riskLevel),
                              ),
                            ),
                            child: Text(
                              dishResult.riskLevel.displayName,
                              style: TextStyle(
                                fontSize: 10,
                                color: dishResult.riskLevel.color,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 8),

                      // 检测到的配料
                      if (dishResult.detectedIngredients.isNotEmpty) ...[
                        const Text(
                          '配料:',
                          style: TextStyle(
                              fontSize: 12, fontWeight: FontWeight.w500),
                        ),
                        const SizedBox(height: 4),
                        Wrap(
                          spacing: 4,
                          runSpacing: 2,
                          children:
                              dishResult.detectedIngredients.map((ingredient) {
                            final isAllergen = dishResult.matchedAllergens
                                    .contains(ingredient) ||
                                dishResult.detectedAllergens
                                    .contains(ingredient);
                            return Container(
                              padding: const EdgeInsets.symmetric(
                                  horizontal: 6, vertical: 2),
                              decoration: BoxDecoration(
                                color: isAllergen
                                    ? Colors.red[50]
                                    : Colors.blue[50],
                                borderRadius: BorderRadius.circular(4),
                                border: Border.all(
                                  color: isAllergen
                                      ? Colors.red[200]!
                                      : Colors.blue[200]!,
                                ),
                              ),
                              child: Text(
                                ingredient,
                                style: TextStyle(
                                  fontSize: 10,
                                  color: isAllergen
                                      ? Colors.red[700]
                                      : Colors.blue[700],
                                ),
                              ),
                            );
                          }).toList(),
                        ),
                      ] else ...[
                        const Text(
                          '配料: 未检测到',
                          style: TextStyle(fontSize: 12, color: Colors.grey),
                        ),
                      ],

                      // 匹配的过敏原
                      if (dishResult.matchedAllergens.isNotEmpty) ...[
                        const SizedBox(height: 6),
                        Row(
                          children: [
                            Icon(Icons.warning,
                                size: 14, color: Colors.red[600]),
                            const SizedBox(width: 4),
                            Text(
                              '过敏原: ${dishResult.matchedAllergens.join('、')}',
                              style: TextStyle(
                                fontSize: 12,
                                color: Colors.red[700],
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ],
                        ),
                      ],
                    ],
                  ),
                );
              }),
            ] else if (result.detectedIngredients.isNotEmpty) ...[
              // 原来的合并显示（作为备用）
              const SizedBox(height: 12),
              const Text(
                '检测到的配料:',
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 4),
              Wrap(
                spacing: 8,
                runSpacing: 4,
                children: result.detectedIngredients.map((ingredient) {
                  return Chip(
                    label: Text(ingredient),
                    backgroundColor: Colors.blue[50],
                  );
                }).toList(),
              ),
            ],

            if (result.matchedAllergens.isNotEmpty) ...[
              const SizedBox(height: 12),
              const Text(
                '匹配的过敏原:',
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 4),
              Wrap(
                spacing: 8,
                runSpacing: 4,
                children: result.matchedAllergens.map((allergen) {
                  return Chip(
                    label: Text(allergen),
                    backgroundColor: Colors.red[50],
                    labelStyle: const TextStyle(color: Colors.red),
                  );
                }).toList(),
              ),
            ],

            if (result.suggestions.isNotEmpty) ...[
              const SizedBox(height: 12),
              const Text(
                '建议:',
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 4),
              ...result.suggestions.map((suggestion) {
                return Padding(
                  padding: const EdgeInsets.only(bottom: 4),
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text('• ', style: TextStyle(fontSize: 16)),
                      Expanded(
                        child: Text(
                          suggestion,
                          style: const TextStyle(fontSize: 14),
                        ),
                      ),
                    ],
                  ),
                );
              }),
            ],
          ],
        ),
      ),
    );
  }

  // 测试宫保鸡丁分析
  void _testGongBaoJiDing() async {
    _testDish('宫保鸡丁', '🧪 宫保鸡丁');
  }

  // 测试白灼虾分析
  void _testBaiZhuoXia() async {
    _testDish('白灼虾', '🦐 白灼虾');
  }

  // 测试西红柿炒鸡蛋分析
  void _testXiHongShiChaoJiDan() async {
    _testDish('西红柿炒鸡蛋', '🥚 西红柿炒鸡蛋');
  }

  // 测试麻婆豆腐分析
  void _testMaPoDouFu() async {
    _testDish('麻婆豆腐', '🌶️ 麻婆豆腐');
  }

  // 🆕 保存检测历史记录
  Future<void> _saveDetectionHistory(
    String originalText,
    List<String> extractedDishes,
    List<AllergenDetectionResult> detectionResults,
    AllergenDetectionResult summaryResult,
  ) async {
    try {
      await DetectionHistoryService.addDetectionRecord(
        originalText: originalText,
        extractedDishes: extractedDishes,
        detectionResults: detectionResults,
        summaryResult: summaryResult,
        source: DetectionSource.ocr,
      );

      if (kDebugMode) {
        print('✅ 检测历史记录已保存');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ 保存检测历史记录失败: $e');
      }
    }
  }

  // 🆕 测试配料检测功能
  void _testIngredientDetection() async {
    print('🚨🚨🚨 测试按钮被点击了！🚨🚨🚨');

    setState(() {
      _isAnalyzingAllergens = true;
      _allergenResult = null;
      _batchResults = null;
    });

    try {
      // 🔥 第一步：测试数据库初始化
      print('🔥 步骤1: 测试数据库初始化...');
      DishDatabaseService.initialize();

      final allDishes = DishDatabaseService.getAllDishes();
      final allIngredients = DishDatabaseService.getAllIngredients();

      print('🔥 数据库状态:');
      print('  菜品数量: ${allDishes.length}');
      print('  配料数量: ${allIngredients.length}');

      if (allDishes.isEmpty) {
        print('🚨 致命错误：数据库中没有菜品！');
        _showErrorDialog('数据库初始化失败：没有菜品数据');
        setState(() {
          _isAnalyzingAllergens = false;
        });
        return;
      }

      // 🔥 第二步：测试单个菜品检测
      print('🔥 步骤2: 测试单个菜品检测...');
      final testDish = '宫保鸡丁';
      print('  测试菜品: $testDish');

      // 先测试数据库搜索
      final searchResults = DishDatabaseService.searchDishes(testDish);
      print('  数据库搜索结果: ${searchResults.length} 个');
      for (var dish in searchResults) {
        print(
            '    找到: ${dish.name} - 配料: ${dish.ingredients} - 过敏原: ${dish.allergens}');
      }

      // 再测试完整检测流程
      final singleResult =
          await IngredientDetectionService.detectAllergens(testDish);
      print('  检测结果:');
      print('    菜品: ${singleResult.dishName}');
      print('    配料: ${singleResult.detectedIngredients}');
      print('    过敏原: ${singleResult.detectedAllergens}');
      print('    风险: ${singleResult.riskLevel.displayName}');

      // 🔥 第三步：创建批量结果以显示详细配料
      final batchResults = [singleResult];

      // 🔥 第四步：显示结果
      setState(() {
        _allergenResult = singleResult;
        _batchResults = batchResults; // 🆕 设置批量结果以显示配料详情
        _isAnalyzingAllergens = false;
      });

      print('🔥 测试完成！');
      print('🔥 最终结果:');
      print('  _allergenResult: ${_allergenResult?.dishName}');
      print('  _batchResults: ${_batchResults?.length}');
      print('  配料数量: ${singleResult.detectedIngredients.length}');
      print('  配料列表: ${singleResult.detectedIngredients}');
      print('  过敏原数量: ${singleResult.detectedAllergens.length}');
      print('  过敏原列表: ${singleResult.detectedAllergens}');
      print('  风险等级: ${singleResult.riskLevel}');
      print('  警告信息: ${singleResult.warning}');

      // 🔥 额外检查UI显示条件
      print('🔥 UI显示条件检查:');
      print('  _batchResults != null: ${_batchResults != null}');
      print('  _batchResults!.isNotEmpty: ${_batchResults?.isNotEmpty}');
      if (_batchResults != null && _batchResults!.isNotEmpty) {
        print('  第一个结果的配料: ${_batchResults![0].detectedIngredients}');
        print('  ✅ 应该显示详细配料信息');
      } else {
        print('  ❌ 不会显示详细配料信息');
      }
    } catch (e, stackTrace) {
      print('🚨 测试失败: $e');
      print('🚨 堆栈跟踪: $stackTrace');
      setState(() {
        _isAnalyzingAllergens = false;
      });
      _showErrorDialog('测试失败: $e');
    }
  }

  // 通用测试方法
  void _testDish(String dishName, String displayName) async {
    if (kDebugMode) {
      print('🧪 测试$displayName分析...');
    }

    setState(() {
      _isAnalyzingAllergens = true;
      _allergenResult = null;
      _batchResults = null; // 🆕 清空批量结果
    });

    try {
      final result = await IngredientDetectionService.detectAllergens(dishName);

      setState(() {
        _allergenResult = result;
        _isAnalyzingAllergens = false;
      });

      if (kDebugMode) {
        print('🧪 $displayName测试结果: ${result.warning}');
      }
    } catch (e) {
      setState(() {
        _isAnalyzingAllergens = false;
      });
      _showErrorDialog('测试失败: $e');
    }
  }

  // 合并批量检测结果
  AllergenDetectionResult _mergeBatchResults(
      List<AllergenDetectionResult> batchResults, List<String> dishNames) {
    if (batchResults.isEmpty) {
      // 如果没有结果，返回默认结果
      return AllergenDetectionResult(
        dishName: '未检测到菜品',
        detectedIngredients: [],
        detectedAllergens: [],
        userAllergens: [],
        matchedAllergens: [],
        riskLevel: AllergenRiskLevel.unknown,
        warning: '未能识别菜品配料',
        suggestions: ['请手动确认菜品配料'],
      );
    }

    // 合并所有配料和过敏原
    final allIngredients = <String>[];
    final allAllergens = <String>[];
    final allMatchedAllergens = <String>[];
    final allSuggestions = <String>[];

    // 找到最高风险等级
    AllergenRiskLevel highestRisk = AllergenRiskLevel.safe;

    for (var result in batchResults) {
      allIngredients.addAll(result.detectedIngredients);
      allAllergens.addAll(result.detectedAllergens);
      allMatchedAllergens.addAll(result.matchedAllergens);
      allSuggestions.addAll(result.suggestions);

      // 更新最高风险等级
      if (result.riskLevel.index > highestRisk.index) {
        highestRisk = result.riskLevel;
      }
    }

    // 去重
    final uniqueIngredients = allIngredients.toSet().toList();
    final uniqueAllergens = allAllergens.toSet().toList();
    final uniqueMatchedAllergens = allMatchedAllergens.toSet().toList();
    final uniqueSuggestions = allSuggestions.toSet().toList();

    // 生成合并后的菜品名称
    final dishNamesDisplay = dishNames.length > 3
        ? '${dishNames.take(3).join('、')}等${dishNames.length}道菜'
        : dishNames.join('、');

    // 生成详细的检测报告
    final detailedWarning = _generateBatchWarning(
        batchResults, uniqueMatchedAllergens, highestRisk);

    // 添加批量检测的特殊建议
    uniqueSuggestions.addAll([
      '本次共检测 ${batchResults.length} 道菜品',
      '建议逐一确认每道菜的配料',
      if (uniqueMatchedAllergens.isNotEmpty) '发现过敏原的菜品请避免食用'
    ]);

    return AllergenDetectionResult(
      dishName: dishNamesDisplay,
      detectedIngredients: uniqueIngredients,
      detectedAllergens: uniqueAllergens,
      userAllergens: batchResults.first.userAllergens,
      matchedAllergens: uniqueMatchedAllergens,
      riskLevel: highestRisk,
      warning: detailedWarning,
      suggestions: uniqueSuggestions.toList(),
    );
  }

  // 生成批量检测的警告信息
  String _generateBatchWarning(List<AllergenDetectionResult> batchResults,
      List<String> matchedAllergens, AllergenRiskLevel highestRisk) {
    if (matchedAllergens.isEmpty) {
      return '批量检测完成，未发现您设置的过敏原，相对安全';
    }

    final riskyDishes = batchResults
        .where((r) => r.matchedAllergens.isNotEmpty)
        .map((r) => r.dishName)
        .toList();

    final allergenList = matchedAllergens.join('、');

    switch (highestRisk) {
      case AllergenRiskLevel.low:
        return '在 ${riskyDishes.join('、')} 中检测到过敏原：$allergenList，请谨慎食用';
      case AllergenRiskLevel.medium:
        return '在 ${riskyDishes.join('、')} 中检测到过敏原：$allergenList，不建议食用';
      case AllergenRiskLevel.high:
        return '在 ${riskyDishes.join('、')} 中检测到多种过敏原：$allergenList，强烈不建议食用';
      default:
        return '在 ${riskyDishes.join('、')} 中检测到过敏原：$allergenList，请注意';
    }
  }

  // 调试：打印所有数据库菜品
  void _debugPrintAllDishes() {
    DishDatabaseService.printAllDishes();
    _showSuccessDialog('数据库信息已打印到控制台，请查看浏览器开发者工具的Console');
  }

  // 测试OCR匹配逻辑
  void _testOcrMatching() {
    // 测试一些常见的OCR识别结果
    final testCases = [
      '宫保鸡丁',
      '宫 保 鸡 丁',
      '宫保鸡丁 15元',
      '麻婆豆腐',
      '麻 婆 豆 腐',
      '白灼虾',
      '西红柿炒鸡蛋',
    ];

    if (kDebugMode) {
      print('🔍 开始测试OCR匹配逻辑...');
      for (var testCase in testCases) {
        DishDatabaseService.testDishMatching(testCase);
      }
    }

    _showSuccessDialog('OCR匹配测试已完成，请查看浏览器开发者工具的Console');
  }

  void _showSuccessDialog(String message) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('提示'),
        content: Text(message),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('确定'),
          ),
        ],
      ),
    );
  }

  void _showErrorDialog(String message) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('错误'),
          content: Text(message),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('确定'),
            ),
          ],
        );
      },
    );
  }

  // 获取风险等级背景颜色
  Color _getRiskLevelBackgroundColor(AllergenRiskLevel riskLevel) {
    switch (riskLevel) {
      case AllergenRiskLevel.safe:
        return const Color(0x334CAF50); // 绿色，20%透明度
      case AllergenRiskLevel.low:
        return const Color(0x33FF9800); // 橙色，20%透明度
      case AllergenRiskLevel.medium:
        return const Color(0x33FF5722); // 深橙色，20%透明度
      case AllergenRiskLevel.high:
        return const Color(0x33F44336); // 红色，20%透明度
      case AllergenRiskLevel.unknown:
        return const Color(0x339E9E9E); // 灰色，20%透明度
    }
  }

  // 获取风险等级边框颜色
  Color _getRiskLevelBorderColor(AllergenRiskLevel riskLevel) {
    switch (riskLevel) {
      case AllergenRiskLevel.safe:
        return const Color(0x804CAF50); // 绿色，50%透明度
      case AllergenRiskLevel.low:
        return const Color(0x80FF9800); // 橙色，50%透明度
      case AllergenRiskLevel.medium:
        return const Color(0x80FF5722); // 深橙色，50%透明度
      case AllergenRiskLevel.high:
        return const Color(0x80F44336); // 红色，50%透明度
      case AllergenRiskLevel.unknown:
        return const Color(0x809E9E9E); // 灰色，50%透明度
    }
  }
}
