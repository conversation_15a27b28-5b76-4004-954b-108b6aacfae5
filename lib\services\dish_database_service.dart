import 'package:flutter/foundation.dart';
import '../models/dish.dart';

// 菜品数据库服务
class DishDatabaseService {
  static List<Dish> _dishes = [];
  static List<Ingredient> _ingredients = [];
  static bool _isInitialized = false;

  // 初始化数据库（同步版本）
  static void initialize() {
    if (_isInitialized) return;

    try {
      _loadIngredients();
      _loadDishes();
      _isInitialized = true;

      if (kDebugMode) {
        print('🔧 菜品数据库初始化完成: ${_dishes.length}道菜, ${_ingredients.length}种配料');
      }
    } catch (e) {
      if (kDebugMode) {
        print('🔧 菜品数据库初始化失败: $e');
      }
    }
  }

  // 异步初始化数据库（保持兼容性）
  static Future<void> initializeAsync() async {
    initialize(); // 调用同步版本
  }

  // 加载配料数据
  static void _loadIngredients() {
    _ingredients = [
      // 海鲜类配料
      Ingredient(
        id: 'shrimp',
        name: '虾',
        category: '海鲜',
        allergens: ['虾'],
        keywords: ['虾', '明虾', '基围虾', '白灼虾', '油焖虾'],
      ),
      Ingredient(
        id: 'crab',
        name: '蟹',
        category: '海鲜',
        allergens: ['蟹'],
        keywords: ['蟹', '螃蟹', '大闸蟹', '梭子蟹', '蟹黄', '蟹肉'],
      ),
      Ingredient(
        id: 'fish',
        name: '鱼',
        category: '海鲜',
        allergens: ['鱼'],
        keywords: ['鱼', '鲈鱼', '草鱼', '鲤鱼', '带鱼', '黄花鱼', '鱼片', '鱼肉'],
      ),

      // 坚果类配料
      Ingredient(
        id: 'peanut',
        name: '花生',
        category: '坚果',
        allergens: ['花生'],
        keywords: ['花生', '花生米', '花生油', '花生酱'],
      ),
      Ingredient(
        id: 'walnut',
        name: '核桃',
        category: '坚果',
        allergens: ['核桃'],
        keywords: ['核桃', '核桃仁', '胡桃'],
      ),
      Ingredient(
        id: 'almond',
        name: '杏仁',
        category: '坚果',
        allergens: ['杏仁'],
        keywords: ['杏仁', '杏仁片', '美国大杏仁'],
      ),

      // 乳制品配料
      Ingredient(
        id: 'milk',
        name: '牛奶',
        category: '乳制品',
        allergens: ['牛奶'],
        keywords: ['牛奶', '鲜奶', '奶油', '淡奶油', '炼乳'],
      ),
      Ingredient(
        id: 'cheese',
        name: '奶酪',
        category: '乳制品',
        allergens: ['奶酪'],
        keywords: ['奶酪', '芝士', '起司', '马苏里拉', '车达'],
      ),

      // 蛋类配料
      Ingredient(
        id: 'egg',
        name: '鸡蛋',
        category: '蛋类',
        allergens: ['鸡蛋'],
        keywords: ['鸡蛋', '蛋', '鸡蛋液', '蛋白', '蛋黄', '蛋花'],
      ),

      // 豆类配料
      Ingredient(
        id: 'soy',
        name: '大豆',
        category: '豆类',
        allergens: ['大豆'],
        keywords: ['大豆', '黄豆', '豆腐', '豆浆', '豆干', '豆皮', '腐竹'],
      ),

      // 谷物类配料
      Ingredient(
        id: 'wheat',
        name: '小麦',
        category: '谷物',
        allergens: ['小麦'],
        keywords: ['面粉', '面条', '面包', '馒头', '饺子皮', '包子皮'],
      ),

      // 🆕 肉类配料（新增）
      Ingredient(
        id: 'chicken',
        name: '鸡肉',
        category: '肉类',
        allergens: [],
        keywords: [
          '鸡肉',
          '鸡',
          '鸡丁',
          '鸡块',
          '鸡胸肉',
          '鸡腿',
          '鸡翅',
          '口水鸡',
          '白切鸡',
          '宫保鸡丁'
        ],
      ),
      Ingredient(
        id: 'pork',
        name: '猪肉',
        category: '肉类',
        allergens: [],
        keywords: [
          '猪肉',
          '猪',
          '五花肉',
          '里脊肉',
          '猪排',
          '猪蹄',
          '猪肚',
          '肉末',
          '肉片',
          '红烧肉'
        ],
      ),
      Ingredient(
        id: 'beef',
        name: '牛肉',
        category: '肉类',
        allergens: [],
        keywords: ['牛肉', '牛', '牛排', '牛腩', '牛筋', '牛肉面', '土豆烧牛肉'],
      ),
      Ingredient(
        id: 'lamb',
        name: '羊肉',
        category: '肉类',
        allergens: [],
        keywords: ['羊肉', '羊', '羊排', '羊腿', '孜然羊肉'],
      ),
      Ingredient(
        id: 'duck',
        name: '鸭肉',
        category: '肉类',
        allergens: [],
        keywords: ['鸭肉', '鸭', '烤鸭', '北京烤鸭', '鸭腿'],
      ),

      // 🆕 豆制品配料（扩展）
      Ingredient(
        id: 'tofu',
        name: '豆腐',
        category: '豆制品',
        allergens: ['大豆'],
        keywords: ['豆腐', '豆干', '豆皮', '豆制品', '内酯豆腐', '老豆腐', '嫩豆腐', '麻婆豆腐'],
      ),

      // 🆕 蔬菜类配料（新增）
      Ingredient(
        id: 'cabbage',
        name: '白菜',
        category: '蔬菜',
        allergens: [],
        keywords: ['白菜', '大白菜', '小白菜', '娃娃菜'],
      ),
      Ingredient(
        id: 'spinach',
        name: '菠菜',
        category: '蔬菜',
        allergens: [],
        keywords: ['菠菜', '菠菜叶'],
      ),
      Ingredient(
        id: 'leek',
        name: '韭菜',
        category: '蔬菜',
        allergens: [],
        keywords: ['韭菜', '韭黄', '韭菜花'],
      ),
      Ingredient(
        id: 'radish',
        name: '萝卜',
        category: '蔬菜',
        allergens: [],
        keywords: ['萝卜', '白萝卜', '胡萝卜', '青萝卜'],
      ),
      Ingredient(
        id: 'potato',
        name: '土豆',
        category: '蔬菜',
        allergens: [],
        keywords: ['土豆', '马铃薯', '洋芋', '薯条', '土豆丝', '土豆块'],
      ),
      Ingredient(
        id: 'eggplant',
        name: '茄子',
        category: '蔬菜',
        allergens: [],
        keywords: ['茄子', '紫茄子', '长茄子'],
      ),
      Ingredient(
        id: 'pepper',
        name: '辣椒',
        category: '蔬菜',
        allergens: [],
        keywords: ['辣椒', '青椒', '红椒', '尖椒', '甜椒', '小米椒', '干辣椒'],
      ),
      Ingredient(
        id: 'tomato',
        name: '西红柿',
        category: '蔬菜',
        allergens: [],
        keywords: ['西红柿', '番茄', '西红柿炒蛋'],
      ),
      Ingredient(
        id: 'mushroom',
        name: '蘑菇',
        category: '蔬菜',
        allergens: [],
        keywords: ['蘑菇', '香菇', '木耳', '金针菇', '平菇'],
      ),
      Ingredient(
        id: 'onion',
        name: '洋葱',
        category: '蔬菜',
        allergens: [],
        keywords: ['洋葱', '葱', '大葱', '小葱', '香葱', '葱花'],
      ),

      // 其他配料
      Ingredient(
        id: 'sesame',
        name: '芝麻',
        category: '其他',
        allergens: ['芝麻'],
        keywords: ['芝麻', '芝麻油', '香油', '芝麻酱', '黑芝麻', '白芝麻'],
      ),
      Ingredient(
        id: 'msg',
        name: '味精',
        category: '其他',
        allergens: ['味精'],
        keywords: ['味精', 'MSG', '鸡精', '调味料'],
      ),

      // === 英语配料数据库（大幅扩展）===

      // 肉类 - 英语
      Ingredient(
        id: 'chicken_en',
        name: 'Chicken',
        category: 'Meat',
        allergens: [],
        keywords: [
          'chicken',
          'poultry',
          'breast',
          'thigh',
          'wing',
          'drumstick',
          'grilled chicken',
          'fried chicken',
          'roasted chicken',
          'chicken breast',
          'chicken thigh',
          'chicken wing',
          'chicken drumstick',
          'chicken tender',
          'chicken fillet',
          'chicken cutlet',
          'chicken strips',
          'buffalo chicken'
        ],
      ),
      Ingredient(
        id: 'beef_en',
        name: 'Beef',
        category: 'Meat',
        allergens: [],
        keywords: [
          'beef',
          'steak',
          'ground beef',
          'beef patty',
          'sirloin',
          'ribeye',
          'filet mignon',
          't-bone',
          'chuck',
          'brisket',
          'short ribs',
          'beef tenderloin',
          'beef roast',
          'prime rib',
          'beef strips',
          'angus beef',
          'wagyu beef',
          'grass-fed beef'
        ],
      ),
      Ingredient(
        id: 'pork_en',
        name: 'Pork',
        category: 'Meat',
        allergens: [],
        keywords: [
          'pork',
          'bacon',
          'ham',
          'sausage',
          'pork chop',
          'pork tenderloin',
          'pork shoulder',
          'pork belly',
          'pulled pork',
          'pork ribs',
          'pancetta',
          'prosciutto',
          'chorizo',
          'bratwurst',
          'pepperoni'
        ],
      ),
      Ingredient(
        id: 'lamb_en',
        name: 'Lamb',
        category: 'Meat',
        allergens: [],
        keywords: [
          'lamb',
          'mutton',
          'lamb chop',
          'leg of lamb',
          'lamb shank',
          'lamb shoulder',
          'rack of lamb',
          'ground lamb',
          'lamb stew'
        ],
      ),

      // 海鲜类 - 英语
      Ingredient(
        id: 'salmon_en',
        name: 'Salmon',
        category: 'Seafood',
        allergens: ['fish'],
        keywords: [
          'salmon',
          'atlantic salmon',
          'pacific salmon',
          'smoked salmon',
          'salmon fillet',
          'salmon steak',
          'grilled salmon',
          'baked salmon'
        ],
      ),
      Ingredient(
        id: 'tuna_en',
        name: 'Tuna',
        category: 'Seafood',
        allergens: ['fish'],
        keywords: [
          'tuna',
          'yellowfin tuna',
          'bluefin tuna',
          'ahi tuna',
          'tuna steak',
          'seared tuna',
          'tuna tartare',
          'canned tuna'
        ],
      ),
      Ingredient(
        id: 'shrimp_en',
        name: 'Shrimp',
        category: 'Seafood',
        allergens: ['shellfish', 'crustaceans'],
        keywords: [
          'shrimp',
          'prawns',
          'jumbo shrimp',
          'tiger shrimp',
          'cocktail shrimp',
          'grilled shrimp',
          'fried shrimp',
          'shrimp scampi',
          'tempura shrimp'
        ],
      ),
      Ingredient(
        id: 'crab_en',
        name: 'Crab',
        category: 'Seafood',
        allergens: ['shellfish', 'crustaceans'],
        keywords: [
          'crab',
          'crab meat',
          'crab cakes',
          'dungeness crab',
          'king crab',
          'snow crab',
          'blue crab',
          'soft shell crab',
          'crab legs'
        ],
      ),
      Ingredient(
        id: 'lobster_en',
        name: 'Lobster',
        category: 'Seafood',
        allergens: ['shellfish', 'crustaceans'],
        keywords: [
          'lobster',
          'lobster tail',
          'lobster bisque',
          'lobster roll',
          'maine lobster',
          'spiny lobster',
          'rock lobster'
        ],
      ),

      // 乳制品 - 英语
      Ingredient(
        id: 'cheese_en',
        name: 'Cheese',
        category: 'Dairy',
        allergens: ['milk', 'dairy'],
        keywords: [
          'cheese',
          'cheddar',
          'mozzarella',
          'parmesan',
          'swiss',
          'gouda',
          'brie',
          'camembert',
          'feta',
          'goat cheese',
          'blue cheese',
          'cream cheese',
          'ricotta',
          'cottage cheese',
          'american cheese',
          'provolone',
          'monterey jack',
          'pepper jack',
          'string cheese'
        ],
      ),
      Ingredient(
        id: 'milk_en',
        name: 'Milk',
        category: 'Dairy',
        allergens: ['milk', 'dairy'],
        keywords: [
          'milk',
          'whole milk',
          'skim milk',
          '2% milk',
          'buttermilk',
          'heavy cream',
          'light cream',
          'half and half',
          'whipped cream',
          'sour cream',
          'crème fraîche'
        ],
      ),
      Ingredient(
        id: 'butter_en',
        name: 'Butter',
        category: 'Dairy',
        allergens: ['milk', 'dairy'],
        keywords: [
          'butter',
          'unsalted butter',
          'salted butter',
          'clarified butter',
          'ghee',
          'compound butter',
          'herb butter',
          'garlic butter'
        ],
      ),

      // 坚果类 - 英语
      Ingredient(
        id: 'peanuts_en',
        name: 'Peanuts',
        category: 'Nuts',
        allergens: ['peanuts'],
        keywords: [
          'peanuts',
          'peanut',
          'roasted peanuts',
          'salted peanuts',
          'peanut butter',
          'peanut oil',
          'crushed peanuts',
          'peanut sauce'
        ],
      ),
      Ingredient(
        id: 'almonds_en',
        name: 'Almonds',
        category: 'Nuts',
        allergens: ['tree nuts', 'almonds'],
        keywords: [
          'almonds',
          'almond',
          'sliced almonds',
          'chopped almonds',
          'almond flour',
          'almond milk',
          'almond butter',
          'marcona almonds'
        ],
      ),
      Ingredient(
        id: 'walnuts_en',
        name: 'Walnuts',
        category: 'Nuts',
        allergens: ['tree nuts', 'walnuts'],
        keywords: [
          'walnuts',
          'walnut',
          'chopped walnuts',
          'walnut pieces',
          'black walnuts',
          'english walnuts',
          'walnut oil'
        ],
      ),
      Ingredient(
        id: 'pecans_en',
        name: 'Pecans',
        category: 'Nuts',
        allergens: ['tree nuts', 'pecans'],
        keywords: [
          'pecans',
          'pecan',
          'pecan halves',
          'chopped pecans',
          'pecan pie'
        ],
      ),

      // 蔬菜类 - 英语
      Ingredient(
        id: 'lettuce_en',
        name: 'Lettuce',
        category: 'Vegetables',
        allergens: [],
        keywords: [
          'lettuce',
          'iceberg lettuce',
          'romaine lettuce',
          'butter lettuce',
          'arugula',
          'spinach',
          'mixed greens',
          'baby greens',
          'spring mix'
        ],
      ),
      Ingredient(
        id: 'tomato_en',
        name: 'Tomato',
        category: 'Vegetables',
        allergens: [],
        keywords: [
          'tomato',
          'tomatoes',
          'cherry tomatoes',
          'grape tomatoes',
          'beefsteak tomato',
          'roma tomatoes',
          'heirloom tomatoes',
          'sun-dried tomatoes',
          'tomato sauce',
          'diced tomatoes'
        ],
      ),
      Ingredient(
        id: 'onion_en',
        name: 'Onion',
        category: 'Vegetables',
        allergens: [],
        keywords: [
          'onion',
          'onions',
          'red onion',
          'white onion',
          'yellow onion',
          'sweet onion',
          'green onions',
          'scallions',
          'shallots',
          'caramelized onions',
          'diced onions',
          'sliced onions'
        ],
      ),
      Ingredient(
        id: 'mushrooms_en',
        name: 'Mushrooms',
        category: 'Vegetables',
        allergens: [],
        keywords: [
          'mushrooms',
          'mushroom',
          'button mushrooms',
          'cremini mushrooms',
          'portobello mushrooms',
          'shiitake mushrooms',
          'oyster mushrooms',
          'porcini mushrooms',
          'morel mushrooms',
          'chanterelle mushrooms'
        ],
      ),

      // === 德语配料数据库（大幅扩展）===

      // 肉类 - 德语
      Ingredient(
        id: 'chicken_de',
        name: 'Hähnchen',
        category: 'Fleisch',
        allergens: [],
        keywords: [
          'hähnchen',
          'huhn',
          'hühnchen',
          'geflügel',
          'hühnerbrust',
          'hähnchenkeule',
          'hähnchenflügel',
          'hähnchenschenkel',
          'grillhähnchen',
          'brathähnchen',
          'hähnchenschnitzel',
          'hähnchenbrust',
          'hähnchenfilet',
          'hähnchensteak'
        ],
      ),
      Ingredient(
        id: 'beef_de',
        name: 'Rindfleisch',
        category: 'Fleisch',
        allergens: [],
        keywords: [
          'rindfleisch',
          'rind',
          'steak',
          'rindersteak',
          'filet',
          'roastbeef',
          'rinderhack',
          'rinderbraten',
          'sauerbraten',
          'rinderschmorbraten',
          'rindergulasch',
          'rinderbrust',
          'entrecôte',
          'ribeye',
          'sirloin'
        ],
      ),
      Ingredient(
        id: 'pork_de',
        name: 'Schweinefleisch',
        category: 'Fleisch',
        allergens: [],
        keywords: [
          'schweinefleisch',
          'schwein',
          'schnitzel',
          'schweinebraten',
          'schweinehack',
          'schweinefilet',
          'schweineschulter',
          'schweinebauch',
          'kassler',
          'speck',
          'schinken',
          'bratwurst',
          'leberwurst',
          'blutwurst',
          'weißwurst'
        ],
      ),
      Ingredient(
        id: 'lamb_de',
        name: 'Lammfleisch',
        category: 'Fleisch',
        allergens: [],
        keywords: [
          'lammfleisch',
          'lamm',
          'lammkeule',
          'lammrücken',
          'lammkoteletts',
          'lammschulter',
          'lammhack'
        ],
      ),

      // 海鲜类 - 德语
      Ingredient(
        id: 'salmon_de',
        name: 'Lachs',
        category: 'Meeresfrüchte',
        allergens: ['fisch'],
        keywords: [
          'lachs',
          'lachsfilet',
          'räucherlachs',
          'wildlachs',
          'atlantiklachs',
          'graved lachs',
          'lachssteak'
        ],
      ),
      Ingredient(
        id: 'tuna_de',
        name: 'Thunfisch',
        category: 'Meeresfrüchte',
        allergens: ['fisch'],
        keywords: ['thunfisch', 'thun', 'thunfischsteak', 'gelbflossenthun'],
      ),
      Ingredient(
        id: 'shrimp_de',
        name: 'Garnelen',
        category: 'Meeresfrüchte',
        allergens: ['schalentiere', 'krustentiere'],
        keywords: [
          'garnelen',
          'shrimps',
          'krabben',
          'riesengarnelen',
          'tigergarnelen',
          'nordseegarnelen',
          'scampi'
        ],
      ),
      Ingredient(
        id: 'fish_de',
        name: 'Fisch',
        category: 'Meeresfrüchte',
        allergens: ['fisch'],
        keywords: [
          'fisch',
          'seefisch',
          'süßwasserfisch',
          'forelle',
          'karpfen',
          'zander',
          'barsch',
          'hecht',
          'scholle',
          'kabeljau',
          'seelachs',
          'makrele',
          'hering'
        ],
      ),

      // 乳制品 - 德语
      Ingredient(
        id: 'cheese_de',
        name: 'Käse',
        category: 'Milchprodukte',
        allergens: ['milch', 'laktose'],
        keywords: [
          'käse',
          'gouda',
          'emmentaler',
          'camembert',
          'brie',
          'mozzarella',
          'parmesan',
          'cheddar',
          'frischkäse',
          'quark',
          'hüttenkäse',
          'schafskäse',
          'ziegenkäse',
          'blauschimmelkäse',
          'hartkäse',
          'weichkäse'
        ],
      ),
      Ingredient(
        id: 'milk_de',
        name: 'Milch',
        category: 'Milchprodukte',
        allergens: ['milch', 'laktose'],
        keywords: [
          'milch',
          'vollmilch',
          'magermilch',
          'sahne',
          'schlagsahne',
          'saure sahne',
          'schmand',
          'crème fraîche',
          'buttermilch'
        ],
      ),
      Ingredient(
        id: 'butter_de',
        name: 'Butter',
        category: 'Milchprodukte',
        allergens: ['milch', 'laktose'],
        keywords: [
          'butter',
          'süßrahmbutter',
          'sauerrahmbutter',
          'kräuterbutter',
          'knoblauchbutter',
          'butterschmalz'
        ],
      ),

      // 坚果类 - 德语
      Ingredient(
        id: 'nuts_de',
        name: 'Nüsse',
        category: 'Nüsse',
        allergens: ['nüsse', 'schalenfrüchte'],
        keywords: [
          'nüsse',
          'erdnüsse',
          'mandeln',
          'walnüsse',
          'haselnüsse',
          'pistazien',
          'cashewnüsse',
          'paranüsse',
          'pekannüsse',
          'macadamianüsse',
          'pinienkerne'
        ],
      ),

      // 蔬菜类 - 德语
      Ingredient(
        id: 'vegetables_de',
        name: 'Gemüse',
        category: 'Gemüse',
        allergens: [],
        keywords: [
          'gemüse',
          'salat',
          'kopfsalat',
          'eisbergsalat',
          'rucola',
          'spinat',
          'tomaten',
          'gurken',
          'paprika',
          'zwiebeln',
          'knoblauch',
          'möhren',
          'karotten',
          'kartoffeln',
          'brokkoli',
          'blumenkohl',
          'zucchini',
          'auberginen',
          'pilze',
          'champignons'
        ],
      ),

      // 谷物类 - 德语
      Ingredient(
        id: 'grains_de',
        name: 'Getreide',
        category: 'Getreide',
        allergens: ['gluten', 'weizen'],
        keywords: [
          'getreide',
          'weizen',
          'roggen',
          'gerste',
          'hafer',
          'dinkel',
          'mehl',
          'weizenmehl',
          'vollkornmehl',
          'brot',
          'brötchen',
          'nudeln',
          'pasta',
          'reis',
          'quinoa',
          'bulgur'
        ],
      ),
    ];
  }

  // 加载菜品数据
  static void _loadDishes() {
    _dishes = [
      // 海鲜类菜品
      Dish(
        id: 'dish_shrimp_1',
        name: '白灼虾',
        category: '海鲜',
        ingredients: ['虾', '生抽', '香油'],
        allergens: ['虾', '芝麻'],
        description: '新鲜虾用开水烫熟，蘸调料食用',
      ),
      Dish(
        id: 'dish_shrimp_2',
        name: '油焖大虾',
        category: '海鲜',
        ingredients: ['虾', '料酒', '生抽', '糖', '葱', '姜'],
        allergens: ['虾'],
        description: '大虾用油焖制，味道鲜美',
      ),
      Dish(
        id: 'dish_crab_1',
        name: '清蒸大闸蟹',
        category: '海鲜',
        ingredients: ['大闸蟹', '生姜', '醋'],
        allergens: ['蟹'],
        description: '新鲜大闸蟹清蒸，保持原味',
      ),
      Dish(
        id: 'dish_fish_1',
        name: '红烧鲈鱼',
        category: '海鲜',
        ingredients: ['鲈鱼', '生抽', '老抽', '料酒', '糖', '葱', '姜'],
        allergens: ['鱼'],
        description: '鲈鱼红烧，色泽红亮，味道鲜美',
      ),

      // 家常菜
      Dish(
        id: 'dish_tofu_1',
        name: '麻婆豆腐',
        category: '家常菜',
        ingredients: ['豆腐', '肉末', '豆瓣酱', '花椒', '葱'],
        allergens: ['大豆'],
        description: '四川名菜，麻辣鲜香',
      ),
      Dish(
        id: 'dish_egg_1',
        name: '西红柿炒鸡蛋',
        category: '家常菜',
        ingredients: ['西红柿', '鸡蛋', '糖', '盐'],
        allergens: ['鸡蛋'],
        description: '经典家常菜，酸甜可口',
      ),
      Dish(
        id: 'dish_pork_1',
        name: '宫保鸡丁',
        category: '川菜',
        ingredients: ['鸡肉', '花生米', '干辣椒', '花椒', '葱', '姜', '蒜'],
        allergens: ['花生'],
        description: '川菜经典，鸡肉嫩滑，花生香脆',
      ),

      // 面食类
      Dish(
        id: 'dish_noodle_1',
        name: '牛肉面',
        category: '面食',
        ingredients: ['面条', '牛肉', '萝卜', '香菜', '辣椒油'],
        allergens: ['小麦'],
        description: '兰州特色，汤清肉烂面筋道',
      ),
      Dish(
        id: 'dish_dumpling_1',
        name: '韭菜鸡蛋饺子',
        category: '面食',
        ingredients: ['饺子皮', '韭菜', '鸡蛋', '香油'],
        allergens: ['小麦', '鸡蛋', '芝麻'],
        description: '素馅饺子，韭菜鸡蛋香',
      ),

      // 甜品类
      Dish(
        id: 'dish_dessert_1',
        name: '核桃酥',
        category: '甜品',
        ingredients: ['面粉', '核桃', '黄油', '糖', '鸡蛋'],
        allergens: ['小麦', '核桃', '牛奶', '鸡蛋'],
        description: '酥脆香甜，核桃香浓',
      ),

      // 川菜类
      Dish(
        id: 'dish_sichuan_1',
        name: '水煮肉片',
        category: '川菜',
        ingredients: ['猪肉', '豆芽', '豆瓣酱', '花椒', '辣椒', '豆腐'],
        allergens: ['大豆'],
        description: '四川名菜，麻辣鲜香',
      ),
      Dish(
        id: 'dish_sichuan_2',
        name: '回锅肉',
        category: '川菜',
        ingredients: ['猪肉', '豆瓣酱', '甜面酱', '青椒', '蒜苗'],
        allergens: ['大豆', '小麦'],
        description: '川菜经典，肥而不腻',
      ),
      Dish(
        id: 'dish_sichuan_3',
        name: '麻辣鱼',
        category: '川菜',
        ingredients: ['鱼', '豆瓣酱', '花椒', '辣椒', '豆腐'],
        allergens: ['鱼', '大豆'],
        description: '麻辣鲜香，鱼肉嫩滑',
      ),

      // 湘菜类
      Dish(
        id: 'dish_hunan_1',
        name: '糖醋里脊',
        category: '湘菜',
        ingredients: ['猪肉', '面粉', '鸡蛋', '糖', '醋'],
        allergens: ['小麦', '鸡蛋'],
        description: '酸甜可口，外酥内嫩',
      ),
      Dish(
        id: 'dish_hunan_2',
        name: '糖醋鱼',
        category: '湘菜',
        ingredients: ['鱼', '面粉', '鸡蛋', '糖', '醋'],
        allergens: ['鱼', '小麦', '鸡蛋'],
        description: '酸甜鲜美，鱼肉鲜嫩',
      ),

      // 东北菜类
      Dish(
        id: 'dish_dongbei_1',
        name: '酸菜鱼',
        category: '东北菜',
        ingredients: ['鱼', '酸菜', '豆腐', '粉条'],
        allergens: ['鱼', '大豆'],
        description: '酸辣开胃，鱼肉鲜美',
      ),
      Dish(
        id: 'dish_dongbei_2',
        name: '红烧鸡块',
        category: '东北菜',
        ingredients: ['鸡肉', '土豆', '生抽', '老抽', '糖'],
        allergens: [],
        description: '色泽红亮，鸡肉软烂',
      ),
      Dish(
        id: 'dish_dongbei_3',
        name: '清炖鸡块',
        category: '东北菜',
        ingredients: ['鸡肉', '蘑菇', '土豆', '胡萝卜'],
        allergens: [],
        description: '汤清味鲜，营养丰富',
      ),

      // 京菜类
      Dish(
        id: 'dish_beijing_1',
        name: '北京烤鸭',
        category: '京菜',
        ingredients: ['鸭肉', '面饼', '甜面酱', '黄瓜', '大葱'],
        allergens: ['小麦', '大豆'],
        description: '皮脆肉嫩，香味浓郁',
      ),

      // 家常菜扩展
      Dish(
        id: 'dish_home_1',
        name: '香菇肉片',
        category: '家常菜',
        ingredients: ['猪肉', '香菇', '生抽', '料酒', '淀粉'],
        allergens: [],
        description: '香菇鲜美，肉片嫩滑',
      ),
      Dish(
        id: 'dish_home_2',
        name: '木耳肉片',
        category: '家常菜',
        ingredients: ['猪肉', '木耳', '生抽', '料酒', '淀粉'],
        allergens: [],
        description: '木耳爽脆，肉片鲜嫩',
      ),
      Dish(
        id: 'dish_home_3',
        name: '土豆烧牛肉',
        category: '家常菜',
        ingredients: ['牛肉', '土豆', '生抽', '老抽', '料酒'],
        allergens: [],
        description: '牛肉软烂，土豆绵糯',
      ),
      Dish(
        id: 'dish_home_4',
        name: '可乐鸡',
        category: '家常菜',
        ingredients: ['鸡肉', '可乐', '生抽', '料酒', '姜'],
        allergens: [],
        description: '甜香可口，鸡肉嫩滑',
      ),

      // 新疆菜
      Dish(
        id: 'dish_xinjiang_1',
        name: '孜然羊肉',
        category: '新疆菜',
        ingredients: ['羊肉', '孜然', '辣椒粉', '洋葱'],
        allergens: [],
        description: '香辣浓郁，羊肉鲜美',
      ),

      // 豆腐菜品
      Dish(
        id: 'dish_tofu_2',
        name: '豆腐炖鱼',
        category: '家常菜',
        ingredients: ['豆腐', '鱼', '生抽', '料酒', '葱', '姜'],
        allergens: ['大豆', '鱼'],
        description: '豆腐嫩滑，鱼肉鲜美',
      ),

      // 更多常见海鲜菜品
      Dish(
        id: 'boiled_shrimp',
        name: '白灼虾',
        category: '海鲜',
        ingredients: ['虾', '生抽', '香葱', '生姜'],
        allergens: ['虾'],
        description: '新鲜虾类白灼制作',
      ),
      Dish(
        id: 'steamed_crab',
        name: '清蒸蟹',
        category: '海鲜',
        ingredients: ['螃蟹', '生姜', '黄酒'],
        allergens: ['蟹'],
        description: '新鲜螃蟹清蒸',
      ),
      Dish(
        id: 'sweet_sour_fish',
        name: '糖醋鱼',
        category: '海鲜',
        ingredients: ['鱼', '糖', '醋', '番茄酱'],
        allergens: ['鱼'],
        description: '酸甜可口的鱼类菜品',
      ),
      Dish(
        id: 'steamed_fish',
        name: '清蒸鱼',
        category: '海鲜',
        ingredients: ['鱼', '生抽', '蒸鱼豉油', '葱丝'],
        allergens: ['鱼'],
        description: '清淡鲜美的蒸鱼',
      ),

      // 蛋类菜品
      Dish(
        id: 'scrambled_eggs',
        name: '炒鸡蛋',
        category: '蛋类',
        ingredients: ['鸡蛋', '盐', '油'],
        allergens: ['鸡蛋'],
        description: '简单的炒蛋',
      ),
      Dish(
        id: 'egg_tomato',
        name: '西红柿炒蛋',
        category: '蛋类',
        ingredients: ['鸡蛋', '西红柿', '糖', '盐'],
        allergens: ['鸡蛋'],
        description: '经典家常菜',
      ),
      Dish(
        id: 'steamed_egg',
        name: '蒸蛋',
        category: '蛋类',
        ingredients: ['鸡蛋', '水', '盐', '香油'],
        allergens: ['鸡蛋'],
        description: '嫩滑的蒸蛋',
      ),

      // 乳制品相关
      Dish(
        id: 'milk_tea',
        name: '奶茶',
        category: '饮品',
        ingredients: ['牛奶', '茶叶', '糖'],
        allergens: ['牛奶'],
        description: '含乳制品的茶饮',
      ),
      Dish(
        id: 'cheese_cake',
        name: '芝士蛋糕',
        category: '甜品',
        ingredients: ['奶酪', '鸡蛋', '面粉', '糖'],
        allergens: ['奶酪', '鸡蛋', '小麦'],
        description: '含多种过敏原的甜品',
      ),

      // 坚果类菜品
      Dish(
        id: 'walnut_cake',
        name: '核桃蛋糕',
        category: '甜品',
        ingredients: ['核桃', '鸡蛋', '面粉', '糖'],
        allergens: ['核桃', '鸡蛋', '小麦'],
        description: '含坚果的蛋糕',
      ),
      Dish(
        id: 'almond_tofu',
        name: '杏仁豆腐',
        category: '甜品',
        ingredients: ['杏仁', '豆腐', '糖'],
        allergens: ['杏仁', '大豆'],
        description: '传统甜品',
      ),
      Dish(
        id: 'peanut_soup',
        name: '花生汤',
        category: '甜品',
        ingredients: ['花生', '糖', '水'],
        allergens: ['花生'],
        description: '香甜的花生汤',
      ),

      // 面食类
      Dish(
        id: 'sesame_noodles',
        name: '芝麻面条',
        category: '主食',
        ingredients: ['面条', '芝麻油', '酱油'],
        allergens: ['小麦', '芝麻'],
        description: '含芝麻的面食',
      ),
      Dish(
        id: 'egg_noodles',
        name: '鸡蛋面',
        category: '主食',
        ingredients: ['面条', '鸡蛋', '青菜'],
        allergens: ['小麦', '鸡蛋'],
        description: '含蛋的面条',
      ),

      // 更多虾类菜品
      Dish(
        id: 'fried_shrimp',
        name: '油焖虾',
        category: '海鲜',
        ingredients: ['虾', '料酒', '生抽', '糖'],
        allergens: ['虾'],
        description: '红烧口味的虾',
      ),
      Dish(
        id: 'shrimp_balls',
        name: '虾球',
        category: '海鲜',
        ingredients: ['虾仁', '淀粉', '蛋白'],
        allergens: ['虾', '鸡蛋'],
        description: 'Q弹的虾球',
      ),

      // 更多蟹类菜品
      Dish(
        id: 'crab_meat_tofu',
        name: '蟹肉豆腐',
        category: '海鲜',
        ingredients: ['蟹肉', '豆腐', '蛋白'],
        allergens: ['蟹', '大豆', '鸡蛋'],
        description: '鲜美的蟹肉豆腐',
      ),
      Dish(
        id: 'crab_roe_noodles',
        name: '蟹黄面',
        category: '主食',
        ingredients: ['面条', '蟹黄', '高汤'],
        allergens: ['小麦', '蟹'],
        description: '鲜美的蟹黄面条',
      ),

      // === 粤菜系列 ===
      Dish(
        id: 'cantonese_roast_duck',
        name: '广式烧鸭',
        category: '粤菜',
        ingredients: ['鸭肉', '五香粉', '生抽', '老抽', '蜂蜜'],
        allergens: [],
        description: '广东传统烧鸭，皮脆肉嫩',
      ),
      Dish(
        id: 'dim_sum_shrimp_dumpling',
        name: '虾饺',
        category: '粤菜',
        ingredients: ['虾仁', '澄粉', '猪肉', '竹笋'],
        allergens: ['虾', '小麦'],
        description: '广式茶点经典，晶莹剔透',
      ),
      Dish(
        id: 'siu_mai',
        name: '烧卖',
        category: '粤菜',
        ingredients: ['猪肉', '虾仁', '香菇', '糯米'],
        allergens: ['虾', '小麦'],
        description: '广式茶点，肉质鲜美',
      ),
      Dish(
        id: 'char_siu',
        name: '叉烧',
        category: '粤菜',
        ingredients: ['猪肉', '叉烧酱', '蜂蜜', '生抽'],
        allergens: ['大豆'],
        description: '广式烧腊，甜香可口',
      ),
      Dish(
        id: 'white_cut_chicken',
        name: '白切鸡',
        category: '粤菜',
        ingredients: ['鸡肉', '姜', '葱', '生抽'],
        allergens: ['大豆'],
        description: '粤菜经典，原汁原味',
      ),
      Dish(
        id: 'steamed_fish',
        name: '清蒸石斑鱼',
        category: '粤菜',
        ingredients: ['石斑鱼', '蒸鱼豉油', '葱丝', '姜丝'],
        allergens: ['鱼', '大豆'],
        description: '粤菜海鲜代表，鲜嫩清香',
      ),
      Dish(
        id: 'honey_bbq_pork',
        name: '蜜汁叉烧',
        category: '粤菜',
        ingredients: ['猪肉', '蜂蜜', '叉烧酱', '料酒'],
        allergens: ['大豆'],
        description: '甜香叉烧，色泽诱人',
      ),
      Dish(
        id: 'wonton_noodles',
        name: '云吞面',
        category: '粤菜',
        ingredients: ['面条', '云吞', '虾仁', '猪肉', '韭黄'],
        allergens: ['小麦', '虾', '鸡蛋'],
        description: '港式经典面食',
      ),

      // === 鲁菜系列 ===
      Dish(
        id: 'sweet_sour_carp',
        name: '糖醋鲤鱼',
        category: '鲁菜',
        ingredients: ['鲤鱼', '糖', '醋', '番茄酱', '淀粉'],
        allergens: ['鱼'],
        description: '鲁菜经典，酸甜可口',
      ),
      Dish(
        id: 'braised_sea_cucumber',
        name: '红烧海参',
        category: '鲁菜',
        ingredients: ['海参', '生抽', '老抽', '糖', '葱'],
        allergens: ['大豆'],
        description: '鲁菜名菜，营养丰富',
      ),
      Dish(
        id: 'dezhou_chicken',
        name: '德州扒鸡',
        category: '鲁菜',
        ingredients: ['鸡肉', '五香粉', '生抽', '老抽', '糖'],
        allergens: ['大豆'],
        description: '山东名菜，香嫩可口',
      ),
      Dish(
        id: 'jiuzhuan_large_intestine',
        name: '九转大肠',
        category: '鲁菜',
        ingredients: ['猪大肠', '生抽', '老抽', '糖', '醋'],
        allergens: ['大豆'],
        description: '鲁菜传统名菜',
      ),

      // === 苏菜系列 ===
      Dish(
        id: 'squirrel_fish',
        name: '松鼠桂鱼',
        category: '苏菜',
        ingredients: ['桂鱼', '糖', '醋', '番茄酱', '淀粉'],
        allergens: ['鱼'],
        description: '苏菜名菜，造型美观',
      ),
      Dish(
        id: 'dongpo_pork',
        name: '东坡肉',
        category: '苏菜',
        ingredients: ['五花肉', '生抽', '老抽', '糖', '料酒'],
        allergens: ['大豆'],
        description: '苏菜经典，肥而不腻',
      ),
      Dish(
        id: 'beggar_chicken',
        name: '叫花鸡',
        category: '苏菜',
        ingredients: ['鸡肉', '荷叶', '泥土', '香料'],
        allergens: [],
        description: '苏菜特色，香嫩可口',
      ),
      Dish(
        id: 'crystal_shrimp',
        name: '水晶虾仁',
        category: '苏菜',
        ingredients: ['虾仁', '蛋清', '淀粉', '料酒'],
        allergens: ['虾', '鸡蛋'],
        description: '苏菜精品，晶莹剔透',
      ),

      // === 湘菜系列 ===
      Dish(
        id: 'spicy_tofu',
        name: '麻辣豆腐',
        category: '湘菜',
        ingredients: ['豆腐', '辣椒', '花椒', '豆瓣酱'],
        allergens: ['大豆'],
        description: '湘菜经典，麻辣鲜香',
      ),
      Dish(
        id: 'steamed_fish_head',
        name: '剁椒鱼头',
        category: '湘菜',
        ingredients: ['鱼头', '剁椒', '蒸鱼豉油', '葱', '姜'],
        allergens: ['鱼', '大豆'],
        description: '湘菜名菜，鲜辣开胃',
      ),
      Dish(
        id: 'spicy_chicken',
        name: '口水鸡',
        category: '湘菜',
        ingredients: ['鸡肉', '辣椒油', '花椒', '蒜泥', '生抽'],
        allergens: ['大豆', '芝麻'],
        description: '湘菜凉菜，麻辣鲜香',
      ),
      Dish(
        id: 'hunan_stir_fry',
        name: '湘式小炒肉',
        category: '湘菜',
        ingredients: ['猪肉', '青椒', '豆豉', '生抽', '料酒'],
        allergens: ['大豆'],
        description: '湘菜家常菜，下饭神器',
      ),

      // === 闽菜系列 ===
      Dish(
        id: 'buddha_jumps_wall',
        name: '佛跳墙',
        category: '闽菜',
        ingredients: ['海参', '鲍鱼', '鱼翅', '干贝', '花胶'],
        allergens: ['鱼'],
        description: '闽菜之王，营养丰富',
      ),
      Dish(
        id: 'fujian_fried_rice',
        name: '福建炒饭',
        category: '闽菜',
        ingredients: ['米饭', '虾仁', '叉烧', '鸡蛋', '青豆'],
        allergens: ['虾', '鸡蛋', '大豆'],
        description: '闽菜经典炒饭',
      ),
      Dish(
        id: 'oyster_omelette',
        name: '蚵仔煎',
        category: '闽菜',
        ingredients: ['牡蛎', '鸡蛋', '地瓜粉', '韭菜'],
        allergens: ['鸡蛋'],
        description: '闽南特色小吃',
      ),

      // === 徽菜系列 ===
      Dish(
        id: 'stewed_turtle',
        name: '红烧甲鱼',
        category: '徽菜',
        ingredients: ['甲鱼', '生抽', '老抽', '糖', '料酒'],
        allergens: ['大豆'],
        description: '徽菜名菜，滋补佳品',
      ),
      Dish(
        id: 'anhui_stewed_chicken',
        name: '徽州炖鸡',
        category: '徽菜',
        ingredients: ['鸡肉', '火腿', '冬笋', '香菇'],
        allergens: [],
        description: '徽菜传统名菜',
      ),

      // === 东北菜系列 ===
      Dish(
        id: 'northeast_stew',
        name: '东北乱炖',
        category: '东北菜',
        ingredients: ['猪肉', '土豆', '豆角', '茄子', '玉米'],
        allergens: [],
        description: '东北特色大锅菜',
      ),
      Dish(
        id: 'pot_wrapped_meat',
        name: '锅包肉',
        category: '东北菜',
        ingredients: ['猪肉', '糖', '醋', '淀粉', '胡萝卜'],
        allergens: [],
        description: '东北名菜，酸甜可口',
      ),
      Dish(
        id: 'northeast_dumplings',
        name: '东北饺子',
        category: '东北菜',
        ingredients: ['面粉', '猪肉', '韭菜', '白菜'],
        allergens: ['小麦'],
        description: '东北传统主食',
      ),
      Dish(
        id: 'sauerkraut_fish',
        name: '酸菜鱼',
        category: '东北菜',
        ingredients: ['鱼肉', '酸菜', '辣椒', '花椒'],
        allergens: ['鱼'],
        description: '东北经典鱼菜',
      ),

      // === 西北菜系列 ===
      Dish(
        id: 'xinjiang_lamb',
        name: '新疆羊肉串',
        category: '西北菜',
        ingredients: ['羊肉', '孜然', '辣椒粉', '盐'],
        allergens: [],
        description: '新疆特色烧烤',
      ),
      Dish(
        id: 'lanzhou_noodles',
        name: '兰州拉面',
        category: '西北菜',
        ingredients: ['面条', '牛肉', '萝卜', '香菜', '辣椒油'],
        allergens: ['小麦'],
        description: '兰州特色面食',
      ),
      Dish(
        id: 'xinjiang_pilaf',
        name: '新疆手抓饭',
        category: '西北菜',
        ingredients: ['大米', '羊肉', '胡萝卜', '洋葱'],
        allergens: [],
        description: '新疆传统主食',
      ),

      // === 快餐外卖常见菜品 ===
      Dish(
        id: 'fried_chicken_burger',
        name: '炸鸡汉堡',
        category: '快餐',
        ingredients: ['鸡肉', '面包', '生菜', '番茄', '沙拉酱'],
        allergens: ['小麦', '鸡蛋', '大豆'],
        description: '西式快餐经典',
      ),
      Dish(
        id: 'beef_noodles',
        name: '红烧牛肉面',
        category: '快餐',
        ingredients: ['面条', '牛肉', '萝卜', '香菜'],
        allergens: ['小麦'],
        description: '中式快餐主食',
      ),
      Dish(
        id: 'fried_rice_with_egg',
        name: '蛋炒饭',
        category: '快餐',
        ingredients: ['米饭', '鸡蛋', '葱', '生抽'],
        allergens: ['鸡蛋', '大豆'],
        description: '经典炒饭',
      ),
      Dish(
        id: 'chicken_rice',
        name: '鸡肉饭',
        category: '快餐',
        ingredients: ['米饭', '鸡肉', '青菜', '生抽'],
        allergens: ['大豆'],
        description: '简单营养套餐',
      ),
      Dish(
        id: 'pork_rice_bowl',
        name: '猪肉盖饭',
        category: '快餐',
        ingredients: ['米饭', '猪肉', '洋葱', '生抽'],
        allergens: ['大豆'],
        description: '日式盖饭',
      ),
      Dish(
        id: 'spicy_hot_pot',
        name: '麻辣烫',
        category: '快餐',
        ingredients: ['面条', '豆腐', '青菜', '辣椒', '花椒'],
        allergens: ['小麦', '大豆'],
        description: '四川特色小吃',
      ),
      Dish(
        id: 'fried_noodles',
        name: '炒面',
        category: '快餐',
        ingredients: ['面条', '豆芽', '韭菜', '生抽'],
        allergens: ['小麦', '大豆'],
        description: '经典炒面',
      ),
      Dish(
        id: 'wonton_soup',
        name: '馄饨',
        category: '快餐',
        ingredients: ['馄饨皮', '猪肉', '虾仁', '紫菜', '榨菜'],
        allergens: ['小麦', '虾'],
        description: '传统汤面',
      ),

      // === 素食系列 ===
      Dish(
        id: 'mapo_tofu_vegetarian',
        name: '素麻婆豆腐',
        category: '素食',
        ingredients: ['豆腐', '豆瓣酱', '花椒', '葱', '姜'],
        allergens: ['大豆'],
        description: '素食版麻婆豆腐',
      ),
      Dish(
        id: 'braised_eggplant',
        name: '红烧茄子',
        category: '素食',
        ingredients: ['茄子', '生抽', '老抽', '糖', '蒜'],
        allergens: ['大豆'],
        description: '经典素菜',
      ),
      Dish(
        id: 'stir_fried_spinach',
        name: '清炒菠菜',
        category: '素食',
        ingredients: ['菠菜', '蒜', '盐', '香油'],
        allergens: ['芝麻'],
        description: '简单清爽素菜',
      ),
      Dish(
        id: 'mushroom_vegetables',
        name: '香菇青菜',
        category: '素食',
        ingredients: ['香菇', '青菜', '蒜', '生抽'],
        allergens: ['大豆'],
        description: '营养搭配素菜',
      ),
      Dish(
        id: 'vegetarian_spring_rolls',
        name: '素春卷',
        category: '素食',
        ingredients: ['春卷皮', '豆芽', '韭菜', '胡萝卜', '香菇'],
        allergens: ['小麦'],
        description: '素食春卷',
      ),
      Dish(
        id: 'cold_cucumber',
        name: '凉拌黄瓜',
        category: '素食',
        ingredients: ['黄瓜', '蒜', '醋', '香油', '盐'],
        allergens: ['芝麻'],
        description: '清爽凉菜',
      ),
      Dish(
        id: 'tomato_egg_noodles',
        name: '西红柿鸡蛋面',
        category: '素食',
        ingredients: ['面条', '西红柿', '鸡蛋', '葱', '盐'],
        allergens: ['小麦', '鸡蛋'],
        description: '家常面条',
      ),

      // === 甜品系列 ===
      Dish(
        id: 'sweet_tofu_pudding',
        name: '豆花',
        category: '甜品',
        ingredients: ['豆腐', '糖浆', '花生', '红豆'],
        allergens: ['大豆', '花生'],
        description: '传统甜品',
      ),
      Dish(
        id: 'mango_pudding',
        name: '芒果布丁',
        category: '甜品',
        ingredients: ['芒果', '牛奶', '明胶', '糖'],
        allergens: ['牛奶'],
        description: '港式甜品',
      ),
      Dish(
        id: 'red_bean_ice',
        name: '红豆冰',
        category: '甜品',
        ingredients: ['红豆', '冰', '炼乳', '糖'],
        allergens: ['牛奶'],
        description: '夏日甜品',
      ),
      Dish(
        id: 'egg_tart',
        name: '蛋挞',
        category: '甜品',
        ingredients: ['蛋挞皮', '鸡蛋', '牛奶', '糖'],
        allergens: ['小麦', '鸡蛋', '牛奶'],
        description: '港式茶点',
      ),
      Dish(
        id: 'tiramisu',
        name: '提拉米苏',
        category: '甜品',
        ingredients: ['马斯卡彭奶酪', '咖啡', '可可粉', '鸡蛋'],
        allergens: ['牛奶', '鸡蛋'],
        description: '意式甜品',
      ),

      // === 汤品系列 ===
      Dish(
        id: 'tomato_egg_soup',
        name: '西红柿鸡蛋汤',
        category: '汤品',
        ingredients: ['西红柿', '鸡蛋', '葱', '盐'],
        allergens: ['鸡蛋'],
        description: '家常汤品',
      ),
      Dish(
        id: 'seaweed_egg_soup',
        name: '紫菜蛋花汤',
        category: '汤品',
        ingredients: ['紫菜', '鸡蛋', '虾皮', '香油'],
        allergens: ['鸡蛋', '虾', '芝麻'],
        description: '清淡汤品',
      ),
      Dish(
        id: 'winter_melon_soup',
        name: '冬瓜汤',
        category: '汤品',
        ingredients: ['冬瓜', '排骨', '姜', '盐'],
        allergens: [],
        description: '清热汤品',
      ),
      Dish(
        id: 'corn_soup',
        name: '玉米汤',
        category: '汤品',
        ingredients: ['玉米', '鸡蛋', '淀粉', '盐'],
        allergens: ['鸡蛋'],
        description: '香甜汤品',
      ),
      Dish(
        id: 'mushroom_soup',
        name: '蘑菇汤',
        category: '汤品',
        ingredients: ['蘑菇', '鸡汤', '奶油', '盐'],
        allergens: ['牛奶'],
        description: '西式汤品',
      ),

      // === 烧烤系列 ===
      Dish(
        id: 'grilled_fish',
        name: '烤鱼',
        category: '烧烤',
        ingredients: ['鱼', '辣椒', '花椒', '豆芽', '土豆'],
        allergens: ['鱼'],
        description: '川式烤鱼',
      ),
      Dish(
        id: 'bbq_pork_ribs',
        name: '烤排骨',
        category: '烧烤',
        ingredients: ['排骨', '烧烤酱', '蜂蜜', '孜然'],
        allergens: ['大豆'],
        description: '香嫩烤排骨',
      ),
      Dish(
        id: 'grilled_chicken_wings',
        name: '烤鸡翅',
        category: '烧烤',
        ingredients: ['鸡翅', '生抽', '蜂蜜', '孜然'],
        allergens: ['大豆'],
        description: '经典烤鸡翅',
      ),
      Dish(
        id: 'grilled_vegetables',
        name: '烤蔬菜',
        category: '烧烤',
        ingredients: ['茄子', '土豆', '青椒', '洋葱', '橄榄油'],
        allergens: [],
        description: '健康烤蔬菜',
      ),

      // === 国际菜品 ===
      Dish(
        id: 'spaghetti_bolognese',
        name: '意大利肉酱面',
        category: '西餐',
        ingredients: ['意大利面', '牛肉', '番茄酱', '洋葱', '奶酪'],
        allergens: ['小麦', '牛奶'],
        description: '经典意式面条',
      ),
      Dish(
        id: 'pizza_margherita',
        name: '玛格丽特披萨',
        category: '西餐',
        ingredients: ['面饼', '番茄酱', '马苏里拉奶酪', '罗勒'],
        allergens: ['小麦', '牛奶'],
        description: '经典意式披萨',
      ),
      Dish(
        id: 'caesar_salad',
        name: '凯撒沙拉',
        category: '西餐',
        ingredients: ['生菜', '面包丁', '帕马森奶酪', '凯撒酱'],
        allergens: ['小麦', '牛奶', '鸡蛋'],
        description: '经典西式沙拉',
      ),
      Dish(
        id: 'beef_steak',
        name: '牛排',
        category: '西餐',
        ingredients: ['牛肉', '黑胡椒', '盐', '橄榄油'],
        allergens: [],
        description: '西式牛排',
      ),
      Dish(
        id: 'sushi_salmon',
        name: '三文鱼寿司',
        category: '日料',
        ingredients: ['寿司米', '三文鱼', '海苔', '芥末'],
        allergens: ['鱼'],
        description: '日式寿司',
      ),
      Dish(
        id: 'ramen',
        name: '日式拉面',
        category: '日料',
        ingredients: ['面条', '猪骨汤', '叉烧', '鸡蛋', '海苔'],
        allergens: ['小麦', '鸡蛋'],
        description: '日式拉面',
      ),
      Dish(
        id: 'tempura',
        name: '天妇罗',
        category: '日料',
        ingredients: ['虾', '蔬菜', '面粉', '鸡蛋'],
        allergens: ['虾', '小麦', '鸡蛋'],
        description: '日式炸物',
      ),
      Dish(
        id: 'korean_bbq',
        name: '韩式烤肉',
        category: '韩料',
        ingredients: ['牛肉', '韩式烤肉酱', '生菜', '蒜'],
        allergens: ['大豆'],
        description: '韩式烤肉',
      ),
      Dish(
        id: 'kimchi_fried_rice',
        name: '泡菜炒饭',
        category: '韩料',
        ingredients: ['米饭', '泡菜', '猪肉', '鸡蛋'],
        allergens: ['鸡蛋'],
        description: '韩式炒饭',
      ),
      Dish(
        id: 'pad_thai',
        name: '泰式炒河粉',
        category: '东南亚',
        ingredients: ['河粉', '虾', '豆芽', '花生', '鱼露'],
        allergens: ['虾', '花生', '鱼'],
        description: '泰式经典',
      ),
      Dish(
        id: 'tom_yum_soup',
        name: '冬阴功汤',
        category: '东南亚',
        ingredients: ['虾', '柠檬草', '辣椒', '椰奶', '蘑菇'],
        allergens: ['虾'],
        description: '泰式酸辣汤',
      ),

      // === 更多家常菜 ===
      Dish(
        id: 'braised_pork_belly',
        name: '红烧肉',
        category: '家常菜',
        ingredients: ['五花肉', '生抽', '老抽', '糖', '料酒'],
        allergens: ['大豆'],
        description: '经典红烧肉',
      ),
      Dish(
        id: 'sweet_sour_pork',
        name: '糖醋里脊',
        category: '家常菜',
        ingredients: ['猪里脊', '糖', '醋', '番茄酱', '淀粉'],
        allergens: [],
        description: '酸甜可口',
      ),
      Dish(
        id: 'fish_fragrant_eggplant',
        name: '鱼香茄子',
        category: '家常菜',
        ingredients: ['茄子', '豆瓣酱', '糖', '醋', '蒜'],
        allergens: ['大豆'],
        description: '川菜经典',
      ),
      Dish(
        id: 'kung_pao_shrimp',
        name: '宫保虾球',
        category: '家常菜',
        ingredients: ['虾仁', '花生', '干辣椒', '花椒', '糖'],
        allergens: ['虾', '花生'],
        description: '宫保系列',
      ),
      Dish(
        id: 'steamed_egg',
        name: '蒸蛋',
        category: '家常菜',
        ingredients: ['鸡蛋', '温水', '盐', '香油'],
        allergens: ['鸡蛋', '芝麻'],
        description: '嫩滑蒸蛋',
      ),
      Dish(
        id: 'scrambled_eggs_tomato',
        name: '西红柿炒鸡蛋',
        category: '家常菜',
        ingredients: ['西红柿', '鸡蛋', '糖', '盐'],
        allergens: ['鸡蛋'],
        description: '国民家常菜',
      ),
      Dish(
        id: 'potato_beef',
        name: '土豆炖牛肉',
        category: '家常菜',
        ingredients: ['牛肉', '土豆', '胡萝卜', '生抽', '老抽'],
        allergens: ['大豆'],
        description: '营养炖菜',
      ),
      Dish(
        id: 'cabbage_pork',
        name: '白菜炖粉条',
        category: '家常菜',
        ingredients: ['白菜', '粉条', '猪肉', '生抽'],
        allergens: ['大豆'],
        description: '东北家常菜',
      ),

      // === 美式菜品 ===
      Dish(
        id: 'american_burger',
        name: 'Hamburger',
        category: 'American',
        ingredients: [
          'ground beef',
          'sesame seed bun',
          'iceberg lettuce',
          'tomato slices',
          'red onion',
          'american cheese',
          'pickles',
          'ketchup',
          'mustard',
          'mayonnaise'
        ],
        allergens: [
          'wheat',
          'gluten',
          'milk',
          'dairy',
          'soy',
          'sesame',
          'eggs'
        ],
        description: 'Classic American hamburger with detailed ingredients',
      ),
      Dish(
        id: 'cheeseburger',
        name: 'Cheeseburger',
        category: 'American',
        ingredients: [
          'ground beef patty',
          'cheddar cheese',
          'brioche bun',
          'butter lettuce',
          'beefsteak tomato',
          'white onion',
          'mayonnaise',
          'special sauce'
        ],
        allergens: ['wheat', 'gluten', 'milk', 'dairy', 'soy', 'eggs'],
        description: 'American cheeseburger with premium ingredients',
      ),
      Dish(
        id: 'fried_chicken',
        name: 'Fried Chicken',
        category: 'American',
        ingredients: [
          'chicken pieces',
          'all-purpose flour',
          'buttermilk',
          'eggs',
          'panko breadcrumbs',
          'vegetable oil',
          'paprika',
          'garlic powder',
          'onion powder',
          'cayenne pepper',
          'salt',
          'black pepper'
        ],
        allergens: ['wheat', 'gluten', 'eggs', 'milk', 'dairy'],
        description: 'Southern fried chicken with secret spice blend',
      ),
      Dish(
        id: 'bbq_ribs',
        name: 'BBQ Ribs',
        category: 'American',
        ingredients: [
          'pork baby back ribs',
          'bbq sauce',
          'brown sugar',
          'apple cider vinegar',
          'worcestershire sauce',
          'liquid smoke',
          'paprika',
          'garlic powder',
          'onion powder',
          'mustard powder',
          'chili powder'
        ],
        allergens: ['soy', 'wheat', 'gluten'],
        description: 'American barbecue ribs with smoky sauce',
      ),
      Dish(
        id: 'mac_and_cheese',
        name: 'Mac and Cheese',
        category: 'American',
        ingredients: [
          'elbow macaroni',
          'sharp cheddar cheese',
          'gruyere cheese',
          'whole milk',
          'heavy cream',
          'butter',
          'flour',
          'mustard powder',
          'nutmeg',
          'breadcrumbs'
        ],
        allergens: ['wheat', 'gluten', 'milk', 'dairy', 'eggs'],
        description: 'Classic macaroni and cheese with three-cheese blend',
      ),
      Dish(
        id: 'chicken_wings',
        name: 'Buffalo Wings',
        category: 'American',
        ingredients: ['chicken wings', 'hot sauce', 'butter', 'celery'],
        allergens: ['milk'],
        description: 'Spicy buffalo chicken wings',
      ),
      Dish(
        id: 'clam_chowder',
        name: 'Clam Chowder',
        category: 'American',
        ingredients: ['clams', 'potatoes', 'cream', 'onions', 'celery'],
        allergens: ['shellfish', 'milk'],
        description: 'New England clam chowder',
      ),
      Dish(
        id: 'philly_cheesesteak',
        name: 'Philly Cheesesteak',
        category: 'American',
        ingredients: ['beef', 'cheese', 'onions', 'peppers', 'hoagie roll'],
        allergens: ['wheat', 'milk'],
        description: 'Philadelphia cheesesteak sandwich',
      ),

      // === 英式菜品 ===
      Dish(
        id: 'fish_and_chips',
        name: 'Fish and Chips',
        category: 'British',
        ingredients: ['fish', 'potatoes', 'flour', 'beer batter', 'oil'],
        allergens: ['fish', 'wheat'],
        description: 'Traditional British fish and chips',
      ),
      Dish(
        id: 'bangers_and_mash',
        name: 'Bangers and Mash',
        category: 'British',
        ingredients: ['sausages', 'mashed potatoes', 'onion gravy'],
        allergens: ['wheat', 'milk'],
        description: 'British sausages with mashed potatoes',
      ),
      Dish(
        id: 'beef_wellington',
        name: 'Beef Wellington',
        category: 'British',
        ingredients: ['beef tenderloin', 'puff pastry', 'mushrooms', 'pâté'],
        allergens: ['wheat', 'eggs'],
        description: 'Classic British beef wellington',
      ),
      Dish(
        id: 'shepherds_pie',
        name: 'Shepherd\'s Pie',
        category: 'British',
        ingredients: ['ground lamb', 'mashed potatoes', 'vegetables', 'gravy'],
        allergens: ['milk'],
        description: 'Traditional shepherd\'s pie',
      ),
      Dish(
        id: 'full_english_breakfast',
        name: 'Full English Breakfast',
        category: 'British',
        ingredients: ['eggs', 'bacon', 'sausages', 'baked beans', 'toast'],
        allergens: ['eggs', 'wheat', 'soy'],
        description: 'Traditional English breakfast',
      ),
      Dish(
        id: 'chicken_tikka_masala',
        name: 'Chicken Tikka Masala',
        category: 'British',
        ingredients: ['chicken', 'tomato sauce', 'cream', 'spices', 'rice'],
        allergens: ['milk'],
        description: 'British-Indian curry dish',
      ),

      // === 澳式菜品 ===
      Dish(
        id: 'meat_pie',
        name: 'Meat Pie',
        category: 'Australian',
        ingredients: ['ground beef', 'pastry', 'onions', 'gravy'],
        allergens: ['wheat', 'eggs'],
        description: 'Australian meat pie',
      ),
      Dish(
        id: 'lamington',
        name: 'Lamington',
        category: 'Australian',
        ingredients: ['sponge cake', 'chocolate', 'coconut', 'cream'],
        allergens: ['wheat', 'eggs', 'milk'],
        description: 'Australian lamington cake',
      ),
      Dish(
        id: 'barramundi',
        name: 'Grilled Barramundi',
        category: 'Australian',
        ingredients: ['barramundi', 'lemon', 'herbs', 'olive oil'],
        allergens: ['fish'],
        description: 'Australian grilled barramundi',
      ),
      Dish(
        id: 'kangaroo_steak',
        name: 'Kangaroo Steak',
        category: 'Australian',
        ingredients: ['kangaroo meat', 'native spices', 'vegetables'],
        allergens: [],
        description: 'Australian kangaroo steak',
      ),
      Dish(
        id: 'pavlova',
        name: 'Pavlova',
        category: 'Australian',
        ingredients: ['meringue', 'cream', 'berries', 'kiwi'],
        allergens: ['eggs', 'milk'],
        description: 'Australian pavlova dessert',
      ),

      // === 德式菜品（增强配料表）===
      Dish(
        id: 'bratwurst',
        name: 'Bratwurst',
        category: 'German',
        ingredients: [
          'pork shoulder',
          'beef chuck',
          'natural casings',
          'white pepper',
          'nutmeg',
          'marjoram',
          'sauerkraut',
          'german mustard',
          'rye bread',
          'caraway seeds'
        ],
        allergens: ['wheat', 'gluten', 'milk'],
        description: 'Traditional German bratwurst with sauerkraut',
      ),
      Dish(
        id: 'schnitzel',
        name: 'Wiener Schnitzel',
        category: 'German',
        ingredients: [
          'veal cutlets',
          'fine breadcrumbs',
          'eggs',
          'all-purpose flour',
          'clarified butter',
          'lemon wedges',
          'parsley',
          'salt',
          'white pepper'
        ],
        allergens: ['wheat', 'gluten', 'eggs', 'milk', 'dairy'],
        description: 'Authentic Wiener Schnitzel with lemon',
      ),
      Dish(
        id: 'sauerbraten',
        name: 'Sauerbraten',
        category: 'German',
        ingredients: [
          'beef roast',
          'red wine vinegar',
          'red wine',
          'onions',
          'carrots',
          'bay leaves',
          'juniper berries',
          'cloves',
          'ginger snaps',
          'beef stock'
        ],
        allergens: ['wheat', 'gluten'],
        description: 'Traditional German pot roast with sweet-sour gravy',
      ),
      Dish(
        id: 'pretzel',
        name: 'Laugenbretzel',
        category: 'German',
        ingredients: [
          'bread flour',
          'active dry yeast',
          'warm water',
          'salt',
          'butter',
          'baking soda',
          'coarse salt'
        ],
        allergens: ['wheat', 'gluten', 'milk', 'dairy'],
        description: 'Traditional German soft pretzel with coarse salt',
      ),
      Dish(
        id: 'black_forest_cake',
        name: 'Black Forest Cake',
        category: 'German',
        ingredients: ['chocolate cake', 'cherries', 'cream', 'kirsch'],
        allergens: ['wheat', 'eggs', 'milk'],
        description: 'German Black Forest cake',
      ),

      // === 国际连锁餐厅 ===
      // McDonald's
      Dish(
        id: 'big_mac',
        name: 'Big Mac',
        category: 'Fast Food',
        ingredients: [
          'beef patties',
          'special sauce',
          'lettuce',
          'cheese',
          'pickles',
          'onions',
          'sesame seed bun'
        ],
        allergens: ['wheat', 'milk', 'eggs', 'soy', 'sesame'],
        description: 'McDonald\'s signature burger',
      ),
      Dish(
        id: 'mcnuggets',
        name: 'Chicken McNuggets',
        category: 'Fast Food',
        ingredients: ['chicken', 'breading', 'oil'],
        allergens: ['wheat'],
        description: 'McDonald\'s chicken nuggets',
      ),
      Dish(
        id: 'mcflurry',
        name: 'McFlurry',
        category: 'Fast Food',
        ingredients: ['ice cream', 'cookies', 'candy'],
        allergens: ['milk', 'wheat', 'soy'],
        description: 'McDonald\'s ice cream dessert',
      ),

      // KFC
      Dish(
        id: 'kfc_original',
        name: 'Original Recipe Chicken',
        category: 'Fast Food',
        ingredients: ['chicken', '11 herbs and spices', 'flour', 'oil'],
        allergens: ['wheat'],
        description: 'KFC\'s original recipe fried chicken',
      ),
      Dish(
        id: 'kfc_zinger',
        name: 'Zinger Burger',
        category: 'Fast Food',
        ingredients: ['spicy chicken', 'lettuce', 'mayo', 'bun'],
        allergens: ['wheat', 'eggs', 'soy'],
        description: 'KFC spicy chicken burger',
      ),

      // Subway
      Dish(
        id: 'subway_italian_bmt',
        name: 'Italian B.M.T.',
        category: 'Fast Food',
        ingredients: [
          'salami',
          'pepperoni',
          'ham',
          'cheese',
          'vegetables',
          'bread'
        ],
        allergens: ['wheat', 'milk'],
        description: 'Subway Italian sandwich',
      ),
      Dish(
        id: 'subway_tuna',
        name: 'Tuna Sandwich',
        category: 'Fast Food',
        ingredients: ['tuna', 'mayo', 'vegetables', 'bread'],
        allergens: ['fish', 'wheat', 'eggs'],
        description: 'Subway tuna sandwich',
      ),

      // Pizza Hut
      Dish(
        id: 'pepperoni_pizza',
        name: 'Pepperoni Pizza',
        category: 'Fast Food',
        ingredients: ['pizza dough', 'tomato sauce', 'cheese', 'pepperoni'],
        allergens: ['wheat', 'milk'],
        description: 'Classic pepperoni pizza',
      ),
      Dish(
        id: 'supreme_pizza',
        name: 'Supreme Pizza',
        category: 'Fast Food',
        ingredients: [
          'pizza dough',
          'tomato sauce',
          'cheese',
          'pepperoni',
          'sausage',
          'peppers',
          'onions',
          'mushrooms'
        ],
        allergens: ['wheat', 'milk'],
        description: 'Supreme pizza with multiple toppings',
      ),

      // Starbucks
      Dish(
        id: 'frappuccino',
        name: 'Frappuccino',
        category: 'Beverages',
        ingredients: ['coffee', 'milk', 'ice', 'sugar', 'whipped cream'],
        allergens: ['milk'],
        description: 'Starbucks blended coffee drink',
      ),
      Dish(
        id: 'latte',
        name: 'Latte',
        category: 'Beverages',
        ingredients: ['espresso', 'steamed milk'],
        allergens: ['milk'],
        description: 'Coffee with steamed milk',
      ),
      Dish(
        id: 'croissant',
        name: 'Butter Croissant',
        category: 'Bakery',
        ingredients: ['flour', 'butter', 'yeast', 'milk', 'eggs'],
        allergens: ['wheat', 'milk', 'eggs'],
        description: 'French butter croissant',
      ),

      // === 更多欧洲菜品 ===
      // 法式菜品
      Dish(
        id: 'coq_au_vin',
        name: 'Coq au Vin',
        category: 'French',
        ingredients: ['chicken', 'red wine', 'mushrooms', 'onions', 'bacon'],
        allergens: [],
        description: 'French chicken in wine',
      ),
      Dish(
        id: 'ratatouille',
        name: 'Ratatouille',
        category: 'French',
        ingredients: [
          'eggplant',
          'zucchini',
          'tomatoes',
          'peppers',
          'onions',
          'herbs'
        ],
        allergens: [],
        description: 'French vegetable stew',
      ),
      Dish(
        id: 'french_onion_soup',
        name: 'French Onion Soup',
        category: 'French',
        ingredients: ['onions', 'beef broth', 'cheese', 'bread'],
        allergens: ['wheat', 'milk'],
        description: 'Classic French onion soup',
      ),

      // 意式菜品
      Dish(
        id: 'carbonara',
        name: 'Spaghetti Carbonara',
        category: 'Italian',
        ingredients: [
          'spaghetti',
          'eggs',
          'cheese',
          'pancetta',
          'black pepper'
        ],
        allergens: ['wheat', 'eggs', 'milk'],
        description: 'Roman pasta dish',
      ),
      Dish(
        id: 'risotto',
        name: 'Risotto',
        category: 'Italian',
        ingredients: ['arborio rice', 'broth', 'cheese', 'wine', 'onions'],
        allergens: ['milk'],
        description: 'Italian rice dish',
      ),
      Dish(
        id: 'tiramisu',
        name: 'Tiramisu',
        category: 'Italian',
        ingredients: ['mascarpone', 'coffee', 'ladyfingers', 'cocoa', 'eggs'],
        allergens: ['milk', 'eggs', 'wheat'],
        description: 'Italian coffee dessert',
      ),

      // 西班牙菜品
      Dish(
        id: 'paella',
        name: 'Paella',
        category: 'Spanish',
        ingredients: ['rice', 'saffron', 'seafood', 'chicken', 'vegetables'],
        allergens: ['shellfish'],
        description: 'Spanish rice dish',
      ),
      Dish(
        id: 'tapas',
        name: 'Tapas',
        category: 'Spanish',
        ingredients: ['various small dishes', 'olives', 'cheese', 'ham'],
        allergens: ['milk'],
        description: 'Spanish small plates',
      ),
    ];
  }

  // 获取所有菜品
  static List<Dish> getAllDishes() {
    if (!_isInitialized) {
      initialize();
    }
    return _dishes;
  }

  // 获取所有配料
  static List<Ingredient> getAllIngredients() {
    if (!_isInitialized) {
      initialize();
    }
    return _ingredients;
  }

  // 根据菜名搜索菜品（超级增强版 - 多层次匹配）
  static List<Dish> searchDishes(String dishName) {
    if (!_isInitialized) {
      initialize();
    }

    final query = _cleanSearchText(dishName);

    // 调试信息
    if (kDebugMode) {
      print('🔍 搜索菜品: "$dishName" -> "$query"');
    }

    // 使用评分系统进行匹配
    final scoredResults = <MapEntry<Dish, double>>[];

    for (final dish in _dishes) {
      final dishNameClean = _cleanSearchText(dish.name);
      double score = 0.0;

      // 1. 精确匹配 (100分)
      if (dishNameClean == query) {
        score = 100.0;
        if (kDebugMode) {
          print('  ✅ 精确匹配: ${dish.name} (100分)');
        }
      }
      // 2. 完全包含匹配 (90分)
      else if (dishNameClean.contains(query)) {
        score = 90.0;
        if (kDebugMode) {
          print('  ✅ 完全包含: ${dish.name} (90分)');
        }
      }
      // 3. 被包含匹配 (85分)
      else if (query.contains(dishNameClean)) {
        score = 85.0;
        if (kDebugMode) {
          print('  ✅ 被包含: ${dish.name} (85分)');
        }
      }
      // 4. 高相似度匹配 (70-80分)
      else {
        final similarity = _calculateSimilarity(query, dishNameClean);
        if (similarity > 0.8) {
          score = 70.0 + similarity * 10;
          if (kDebugMode) {
            print(
                '  ✅ 高相似度: ${dish.name} (${score.toStringAsFixed(1)}分, 相似度: ${(similarity * 100).toStringAsFixed(1)}%)');
          }
        }
        // 5. 中等相似度匹配 (50-70分)
        else if (similarity > 0.6) {
          score = 50.0 + similarity * 20;
          if (kDebugMode) {
            print(
                '  ✅ 中等相似度: ${dish.name} (${score.toStringAsFixed(1)}分, 相似度: ${(similarity * 100).toStringAsFixed(1)}%)');
          }
        }
        // 6. 关键字匹配 (30-50分)
        else if (_hasKeywordMatch(query, dishNameClean)) {
          score = 30.0 + _calculateKeywordScore(query, dishNameClean);
          if (kDebugMode) {
            print('  ✅ 关键字匹配: ${dish.name} (${score.toStringAsFixed(1)}分)');
          }
        }
      }

      if (score > 0) {
        scoredResults.add(MapEntry(dish, score));
      }
    }

    // 按分数排序，返回分数大于等于30分的结果
    scoredResults.sort((a, b) => b.value.compareTo(a.value));
    final results = scoredResults
        .where((entry) => entry.value >= 30.0)
        .map((entry) => entry.key)
        .toList();

    if (kDebugMode) {
      print('  搜索结果: ${results.map((d) => d.name).toList()}');
      if (scoredResults.isNotEmpty) {
        print(
            '  最高分: ${scoredResults.first.value.toStringAsFixed(1)}分 - ${scoredResults.first.key.name}');
      }
    }

    return results;
  }

  // 检查是否有关键字匹配
  static bool _hasKeywordMatch(String query, String dishName) {
    // 提取关键字（2个字符以上的子串）
    final queryChars = query.split('');

    // 检查是否有连续的2个字符匹配
    for (int i = 0; i < queryChars.length - 1; i++) {
      final keyword = queryChars[i] + queryChars[i + 1];
      if (dishName.contains(keyword)) {
        return true;
      }
    }

    return false;
  }

  // 计算关键字匹配分数
  static double _calculateKeywordScore(String query, String dishName) {
    double score = 0.0;
    final queryChars = query.split('');

    // 单字符匹配
    for (final char in queryChars) {
      if (dishName.contains(char)) {
        score += 2.0;
      }
    }

    // 连续字符匹配加分
    for (int i = 0; i < queryChars.length - 1; i++) {
      final keyword = queryChars[i] + queryChars[i + 1];
      if (dishName.contains(keyword)) {
        score += 5.0;
      }
    }

    return score.clamp(0.0, 20.0);
  }

  // 清洗搜索文本
  static String _cleanSearchText(String text) {
    String cleaned = text.toLowerCase().trim();

    // 去除空格
    cleaned = cleaned.replaceAll(' ', '');

    // 去除标点符号
    cleaned =
        cleaned.replaceAll(RegExp(r'[，。、；：！？""' '（）【】《》〈〉「」『』〔〕［］｛｝]'), '');
    cleaned = cleaned.replaceAll(RegExp(r'[,.;:!?"\(\)\[\]{}]'), '');

    // 去除数字和价格符号
    cleaned = cleaned.replaceAll(RegExp(r'[0-9￥¥元兀]'), '');

    return cleaned;
  }

  // 计算字符串相似度（增强版 - 使用编辑距离算法）
  static double _calculateSimilarity(String s1, String s2) {
    if (s1.isEmpty || s2.isEmpty) return 0.0;
    if (s1 == s2) return 1.0;

    // 使用编辑距离算法计算相似度
    final editDistance = _calculateEditDistance(s1, s2);
    final maxLength = s1.length > s2.length ? s1.length : s2.length;

    // 转换为相似度（0-1之间）
    final similarity = 1.0 - (editDistance / maxLength);

    // 额外加分：如果包含相同的字符
    final commonChars = _countCommonCharacters(s1, s2);
    final charBonus = commonChars / maxLength * 0.3; // 最多30%的加分

    return (similarity + charBonus).clamp(0.0, 1.0);
  }

  // 计算编辑距离（Levenshtein距离）
  static int _calculateEditDistance(String s1, String s2) {
    final len1 = s1.length;
    final len2 = s2.length;

    // 创建距离矩阵
    final matrix = List.generate(len1 + 1, (i) => List.filled(len2 + 1, 0));

    // 初始化第一行和第一列
    for (int i = 0; i <= len1; i++) {
      matrix[i][0] = i;
    }
    for (int j = 0; j <= len2; j++) {
      matrix[0][j] = j;
    }

    // 填充矩阵
    for (int i = 1; i <= len1; i++) {
      for (int j = 1; j <= len2; j++) {
        final cost = s1[i - 1] == s2[j - 1] ? 0 : 1;
        matrix[i][j] = [
          matrix[i - 1][j] + 1, // 删除
          matrix[i][j - 1] + 1, // 插入
          matrix[i - 1][j - 1] + cost // 替换
        ].reduce((a, b) => a < b ? a : b);
      }
    }

    return matrix[len1][len2];
  }

  // 计算两个字符串的共同字符数
  static int _countCommonCharacters(String s1, String s2) {
    final chars1 = s1.split('');
    final chars2 = s2.split('');
    int commonCount = 0;

    for (final char in chars1) {
      if (chars2.contains(char)) {
        commonCount++;
        chars2.remove(char); // 避免重复计算
      }
    }

    return commonCount;
  }

  // 根据配料搜索菜品
  static List<Dish> searchDishesByIngredient(String ingredient) {
    if (!_isInitialized) {
      initialize();
    }

    return _dishes.where((dish) {
      return dish.ingredients.any((ing) => ing.contains(ingredient));
    }).toList();
  }

  // 获取菜品统计信息
  static Map<String, int> getDishStats() {
    if (!_isInitialized) {
      initialize();
    }

    final stats = <String, int>{};
    for (var dish in _dishes) {
      stats[dish.category] = (stats[dish.category] ?? 0) + 1;
    }
    return stats;
  }

  // 调试：打印所有菜品信息
  static void printAllDishes() {
    if (!_isInitialized) {
      initialize();
    }

    if (kDebugMode) {
      print('📚 数据库中的所有菜品:');
      for (int i = 0; i < _dishes.length; i++) {
        final dish = _dishes[i];
        final cleanName = _cleanSearchText(dish.name);
        print(
            '  ${i + 1}. "${dish.name}" -> "$cleanName" (过敏原: ${dish.allergens})');
        print('     字符编码: ${dish.name.codeUnits}');
      }
      print('📚 总计: ${_dishes.length} 道菜品');
    }
  }

  // 测试特定菜品的匹配
  static void testDishMatching(String testName) {
    if (!_isInitialized) {
      initialize();
    }

    if (kDebugMode) {
      print('🧪 测试菜品匹配: "$testName"');
      final cleanTest = _cleanSearchText(testName);
      print('  清洗后: "$cleanTest"');
      print('  字符编码: ${testName.codeUnits}');

      final matches = searchDishes(testName);
      if (matches.isNotEmpty) {
        print('  ✅ 找到匹配: ${matches.map((d) => d.name).toList()}');
      } else {
        print('  ❌ 未找到匹配');

        // 尝试逐个字符匹配
        print('  🔍 逐个检查数据库菜品:');
        for (var dish in _dishes) {
          final dishClean = _cleanSearchText(dish.name);
          final similarity = _calculateSimilarity(cleanTest, dishClean);
          if (similarity > 0.3) {
            print(
                '    "${dish.name}" -> "$dishClean" (相似度: ${(similarity * 100).toStringAsFixed(1)}%)');
          }
        }
      }
    }
  }

  // === 多语言支持辅助函数 ===

  // 检测查询语言
  static String _detectQueryLanguage(String text) {
    if (text.isEmpty) return 'en';

    int chineseChars = 0;
    int englishChars = 0;
    int germanChars = 0;

    for (int i = 0; i < text.length; i++) {
      final char = text[i];
      final codeUnit = char.codeUnitAt(0);

      if ((codeUnit >= 0x4E00 && codeUnit <= 0x9FFF) ||
          (codeUnit >= 0x3400 && codeUnit <= 0x4DBF)) {
        chineseChars++;
      } else if ((codeUnit >= 65 && codeUnit <= 90) ||
          (codeUnit >= 97 && codeUnit <= 122)) {
        englishChars++;
      } else if (char == 'ä' ||
          char == 'ö' ||
          char == 'ü' ||
          char == 'Ä' ||
          char == 'Ö' ||
          char == 'Ü' ||
          char == 'ß') {
        germanChars++;
      }
    }

    if (chineseChars > englishChars && chineseChars > germanChars) {
      return 'zh';
    } else if (germanChars > 0) {
      return 'de';
    } else {
      return 'en';
    }
  }

  // 根据语言清洗搜索文本
  static String _cleanSearchTextByLanguage(String text, String language) {
    switch (language) {
      case 'zh':
        return _cleanSearchText(text); // 使用原有的中文清洗
      case 'en':
        return _cleanEnglishSearchText(text);
      case 'de':
        return _cleanGermanSearchText(text);
      default:
        return _cleanEnglishSearchText(text);
    }
  }

  // 英文搜索文本清洗
  static String _cleanEnglishSearchText(String text) {
    var cleaned = text.trim().toLowerCase();

    // 保留空格，但规范化
    cleaned = cleaned.replaceAll(RegExp(r'\s+'), ' ');

    // 去除标点符号
    cleaned = cleaned.replaceAll(RegExp(r'[,.;:!?"\(\)\[\]{}]'), '');

    // 去除价格符号
    cleaned = cleaned.replaceAll(RegExp(r'[\$£€]'), '');
    cleaned = cleaned.replaceAll(RegExp(r'\b\d+\.?\d*\b'), '');

    // 去除常见噪音词
    final noiseWords = [
      'menu',
      'special',
      'today',
      'fresh',
      'new',
      'hot',
      'cold'
    ];
    for (final noise in noiseWords) {
      cleaned = cleaned.replaceAll(RegExp(r'\b' + noise + r'\b'), '');
    }

    return cleaned.trim().replaceAll(RegExp(r'\s+'), ' ');
  }

  // 德文搜索文本清洗
  static String _cleanGermanSearchText(String text) {
    var cleaned = text.trim().toLowerCase();

    cleaned = cleaned.replaceAll(RegExp(r'\s+'), ' ');
    cleaned = cleaned.replaceAll(RegExp(r'[,.;:!?"\(\)\[\]{}]'), '');
    cleaned = cleaned.replaceAll(RegExp(r'[€]'), '');
    cleaned = cleaned.replaceAll(RegExp(r'\b\d+[,.]?\d*\b'), '');

    final noiseWords = ['speisekarte', 'spezial', 'heute', 'frisch', 'neu'];
    for (final noise in noiseWords) {
      cleaned = cleaned.replaceAll(RegExp(r'\b' + noise + r'\b'), '');
    }

    return cleaned.trim().replaceAll(RegExp(r'\s+'), ' ');
  }
}
