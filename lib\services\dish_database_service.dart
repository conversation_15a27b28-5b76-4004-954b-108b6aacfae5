import 'package:flutter/foundation.dart';
import '../models/dish.dart';

// 菜品数据库服务
class DishDatabaseService {
  static List<Dish> _dishes = [];
  static List<Ingredient> _ingredients = [];
  static bool _isInitialized = false;

  // 初始化数据库
  static Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      _loadIngredients();
      _loadDishes();
      _isInitialized = true;

      if (kDebugMode) {
        print('菜品数据库初始化完成: ${_dishes.length}道菜, ${_ingredients.length}种配料');
      }
    } catch (e) {
      if (kDebugMode) {
        print('菜品数据库初始化失败: $e');
      }
    }
  }

  // 加载配料数据
  static void _loadIngredients() {
    _ingredients = [
      // 海鲜类配料
      Ingredient(
        id: 'shrimp',
        name: '虾',
        category: '海鲜',
        allergens: ['虾'],
        keywords: ['虾', '明虾', '基围虾', '白灼虾', '油焖虾'],
      ),
      Ingredient(
        id: 'crab',
        name: '蟹',
        category: '海鲜',
        allergens: ['蟹'],
        keywords: ['蟹', '螃蟹', '大闸蟹', '梭子蟹', '蟹黄', '蟹肉'],
      ),
      Ingredient(
        id: 'fish',
        name: '鱼',
        category: '海鲜',
        allergens: ['鱼'],
        keywords: ['鱼', '鲈鱼', '草鱼', '鲤鱼', '带鱼', '黄花鱼', '鱼片', '鱼肉'],
      ),

      // 坚果类配料
      Ingredient(
        id: 'peanut',
        name: '花生',
        category: '坚果',
        allergens: ['花生'],
        keywords: ['花生', '花生米', '花生油', '花生酱'],
      ),
      Ingredient(
        id: 'walnut',
        name: '核桃',
        category: '坚果',
        allergens: ['核桃'],
        keywords: ['核桃', '核桃仁', '胡桃'],
      ),
      Ingredient(
        id: 'almond',
        name: '杏仁',
        category: '坚果',
        allergens: ['杏仁'],
        keywords: ['杏仁', '杏仁片', '美国大杏仁'],
      ),

      // 乳制品配料
      Ingredient(
        id: 'milk',
        name: '牛奶',
        category: '乳制品',
        allergens: ['牛奶'],
        keywords: ['牛奶', '鲜奶', '奶油', '淡奶油', '炼乳'],
      ),
      Ingredient(
        id: 'cheese',
        name: '奶酪',
        category: '乳制品',
        allergens: ['奶酪'],
        keywords: ['奶酪', '芝士', '起司', '马苏里拉', '车达'],
      ),

      // 蛋类配料
      Ingredient(
        id: 'egg',
        name: '鸡蛋',
        category: '蛋类',
        allergens: ['鸡蛋'],
        keywords: ['鸡蛋', '蛋', '鸡蛋液', '蛋白', '蛋黄', '蛋花'],
      ),

      // 豆类配料
      Ingredient(
        id: 'soy',
        name: '大豆',
        category: '豆类',
        allergens: ['大豆'],
        keywords: ['大豆', '黄豆', '豆腐', '豆浆', '豆干', '豆皮', '腐竹'],
      ),

      // 谷物类配料
      Ingredient(
        id: 'wheat',
        name: '小麦',
        category: '谷物',
        allergens: ['小麦'],
        keywords: ['面粉', '面条', '面包', '馒头', '饺子皮', '包子皮'],
      ),

      // 其他配料
      Ingredient(
        id: 'sesame',
        name: '芝麻',
        category: '其他',
        allergens: ['芝麻'],
        keywords: ['芝麻', '芝麻油', '香油', '芝麻酱', '黑芝麻', '白芝麻'],
      ),
      Ingredient(
        id: 'msg',
        name: '味精',
        category: '其他',
        allergens: ['味精'],
        keywords: ['味精', 'MSG', '鸡精', '调味料'],
      ),
    ];
  }

  // 加载菜品数据
  static void _loadDishes() {
    _dishes = [
      // 海鲜类菜品
      Dish(
        id: 'dish_shrimp_1',
        name: '白灼虾',
        category: '海鲜',
        ingredients: ['虾', '生抽', '香油'],
        allergens: ['虾', '芝麻'],
        description: '新鲜虾用开水烫熟，蘸调料食用',
      ),
      Dish(
        id: 'dish_shrimp_2',
        name: '油焖大虾',
        category: '海鲜',
        ingredients: ['虾', '料酒', '生抽', '糖', '葱', '姜'],
        allergens: ['虾'],
        description: '大虾用油焖制，味道鲜美',
      ),
      Dish(
        id: 'dish_crab_1',
        name: '清蒸大闸蟹',
        category: '海鲜',
        ingredients: ['大闸蟹', '生姜', '醋'],
        allergens: ['蟹'],
        description: '新鲜大闸蟹清蒸，保持原味',
      ),
      Dish(
        id: 'dish_fish_1',
        name: '红烧鲈鱼',
        category: '海鲜',
        ingredients: ['鲈鱼', '生抽', '老抽', '料酒', '糖', '葱', '姜'],
        allergens: ['鱼'],
        description: '鲈鱼红烧，色泽红亮，味道鲜美',
      ),

      // 家常菜
      Dish(
        id: 'dish_tofu_1',
        name: '麻婆豆腐',
        category: '家常菜',
        ingredients: ['豆腐', '肉末', '豆瓣酱', '花椒', '葱'],
        allergens: ['大豆'],
        description: '四川名菜，麻辣鲜香',
      ),
      Dish(
        id: 'dish_egg_1',
        name: '西红柿炒鸡蛋',
        category: '家常菜',
        ingredients: ['西红柿', '鸡蛋', '糖', '盐'],
        allergens: ['鸡蛋'],
        description: '经典家常菜，酸甜可口',
      ),
      Dish(
        id: 'dish_pork_1',
        name: '宫保鸡丁',
        category: '川菜',
        ingredients: ['鸡肉', '花生米', '干辣椒', '花椒', '葱', '姜', '蒜'],
        allergens: ['花生'],
        description: '川菜经典，鸡肉嫩滑，花生香脆',
      ),

      // 面食类
      Dish(
        id: 'dish_noodle_1',
        name: '牛肉面',
        category: '面食',
        ingredients: ['面条', '牛肉', '萝卜', '香菜', '辣椒油'],
        allergens: ['小麦'],
        description: '兰州特色，汤清肉烂面筋道',
      ),
      Dish(
        id: 'dish_dumpling_1',
        name: '韭菜鸡蛋饺子',
        category: '面食',
        ingredients: ['饺子皮', '韭菜', '鸡蛋', '香油'],
        allergens: ['小麦', '鸡蛋', '芝麻'],
        description: '素馅饺子，韭菜鸡蛋香',
      ),

      // 甜品类
      Dish(
        id: 'dish_dessert_1',
        name: '核桃酥',
        category: '甜品',
        ingredients: ['面粉', '核桃', '黄油', '糖', '鸡蛋'],
        allergens: ['小麦', '核桃', '牛奶', '鸡蛋'],
        description: '酥脆香甜，核桃香浓',
      ),

      // 川菜类
      Dish(
        id: 'dish_sichuan_1',
        name: '水煮肉片',
        category: '川菜',
        ingredients: ['猪肉', '豆芽', '豆瓣酱', '花椒', '辣椒', '豆腐'],
        allergens: ['大豆'],
        description: '四川名菜，麻辣鲜香',
      ),
      Dish(
        id: 'dish_sichuan_2',
        name: '回锅肉',
        category: '川菜',
        ingredients: ['猪肉', '豆瓣酱', '甜面酱', '青椒', '蒜苗'],
        allergens: ['大豆', '小麦'],
        description: '川菜经典，肥而不腻',
      ),
      Dish(
        id: 'dish_sichuan_3',
        name: '麻辣鱼',
        category: '川菜',
        ingredients: ['鱼', '豆瓣酱', '花椒', '辣椒', '豆腐'],
        allergens: ['鱼', '大豆'],
        description: '麻辣鲜香，鱼肉嫩滑',
      ),

      // 湘菜类
      Dish(
        id: 'dish_hunan_1',
        name: '糖醋里脊',
        category: '湘菜',
        ingredients: ['猪肉', '面粉', '鸡蛋', '糖', '醋'],
        allergens: ['小麦', '鸡蛋'],
        description: '酸甜可口，外酥内嫩',
      ),
      Dish(
        id: 'dish_hunan_2',
        name: '糖醋鱼',
        category: '湘菜',
        ingredients: ['鱼', '面粉', '鸡蛋', '糖', '醋'],
        allergens: ['鱼', '小麦', '鸡蛋'],
        description: '酸甜鲜美，鱼肉鲜嫩',
      ),

      // 东北菜类
      Dish(
        id: 'dish_dongbei_1',
        name: '酸菜鱼',
        category: '东北菜',
        ingredients: ['鱼', '酸菜', '豆腐', '粉条'],
        allergens: ['鱼', '大豆'],
        description: '酸辣开胃，鱼肉鲜美',
      ),
      Dish(
        id: 'dish_dongbei_2',
        name: '红烧鸡块',
        category: '东北菜',
        ingredients: ['鸡肉', '土豆', '生抽', '老抽', '糖'],
        allergens: [],
        description: '色泽红亮，鸡肉软烂',
      ),
      Dish(
        id: 'dish_dongbei_3',
        name: '清炖鸡块',
        category: '东北菜',
        ingredients: ['鸡肉', '蘑菇', '土豆', '胡萝卜'],
        allergens: [],
        description: '汤清味鲜，营养丰富',
      ),

      // 京菜类
      Dish(
        id: 'dish_beijing_1',
        name: '北京烤鸭',
        category: '京菜',
        ingredients: ['鸭肉', '面饼', '甜面酱', '黄瓜', '大葱'],
        allergens: ['小麦', '大豆'],
        description: '皮脆肉嫩，香味浓郁',
      ),

      // 家常菜扩展
      Dish(
        id: 'dish_home_1',
        name: '香菇肉片',
        category: '家常菜',
        ingredients: ['猪肉', '香菇', '生抽', '料酒', '淀粉'],
        allergens: [],
        description: '香菇鲜美，肉片嫩滑',
      ),
      Dish(
        id: 'dish_home_2',
        name: '木耳肉片',
        category: '家常菜',
        ingredients: ['猪肉', '木耳', '生抽', '料酒', '淀粉'],
        allergens: [],
        description: '木耳爽脆，肉片鲜嫩',
      ),
      Dish(
        id: 'dish_home_3',
        name: '土豆烧牛肉',
        category: '家常菜',
        ingredients: ['牛肉', '土豆', '生抽', '老抽', '料酒'],
        allergens: [],
        description: '牛肉软烂，土豆绵糯',
      ),
      Dish(
        id: 'dish_home_4',
        name: '可乐鸡',
        category: '家常菜',
        ingredients: ['鸡肉', '可乐', '生抽', '料酒', '姜'],
        allergens: [],
        description: '甜香可口，鸡肉嫩滑',
      ),

      // 新疆菜
      Dish(
        id: 'dish_xinjiang_1',
        name: '孜然羊肉',
        category: '新疆菜',
        ingredients: ['羊肉', '孜然', '辣椒粉', '洋葱'],
        allergens: [],
        description: '香辣浓郁，羊肉鲜美',
      ),

      // 豆腐菜品
      Dish(
        id: 'dish_tofu_2',
        name: '豆腐炖鱼',
        category: '家常菜',
        ingredients: ['豆腐', '鱼', '生抽', '料酒', '葱', '姜'],
        allergens: ['大豆', '鱼'],
        description: '豆腐嫩滑，鱼肉鲜美',
      ),

      // 更多常见海鲜菜品
      Dish(
        id: 'boiled_shrimp',
        name: '白灼虾',
        category: '海鲜',
        ingredients: ['虾', '生抽', '香葱', '生姜'],
        allergens: ['虾'],
        description: '新鲜虾类白灼制作',
      ),
      Dish(
        id: 'steamed_crab',
        name: '清蒸蟹',
        category: '海鲜',
        ingredients: ['螃蟹', '生姜', '黄酒'],
        allergens: ['蟹'],
        description: '新鲜螃蟹清蒸',
      ),
      Dish(
        id: 'sweet_sour_fish',
        name: '糖醋鱼',
        category: '海鲜',
        ingredients: ['鱼', '糖', '醋', '番茄酱'],
        allergens: ['鱼'],
        description: '酸甜可口的鱼类菜品',
      ),
      Dish(
        id: 'steamed_fish',
        name: '清蒸鱼',
        category: '海鲜',
        ingredients: ['鱼', '生抽', '蒸鱼豉油', '葱丝'],
        allergens: ['鱼'],
        description: '清淡鲜美的蒸鱼',
      ),

      // 蛋类菜品
      Dish(
        id: 'scrambled_eggs',
        name: '炒鸡蛋',
        category: '蛋类',
        ingredients: ['鸡蛋', '盐', '油'],
        allergens: ['鸡蛋'],
        description: '简单的炒蛋',
      ),
      Dish(
        id: 'egg_tomato',
        name: '西红柿炒蛋',
        category: '蛋类',
        ingredients: ['鸡蛋', '西红柿', '糖', '盐'],
        allergens: ['鸡蛋'],
        description: '经典家常菜',
      ),
      Dish(
        id: 'steamed_egg',
        name: '蒸蛋',
        category: '蛋类',
        ingredients: ['鸡蛋', '水', '盐', '香油'],
        allergens: ['鸡蛋'],
        description: '嫩滑的蒸蛋',
      ),

      // 乳制品相关
      Dish(
        id: 'milk_tea',
        name: '奶茶',
        category: '饮品',
        ingredients: ['牛奶', '茶叶', '糖'],
        allergens: ['牛奶'],
        description: '含乳制品的茶饮',
      ),
      Dish(
        id: 'cheese_cake',
        name: '芝士蛋糕',
        category: '甜品',
        ingredients: ['奶酪', '鸡蛋', '面粉', '糖'],
        allergens: ['奶酪', '鸡蛋', '小麦'],
        description: '含多种过敏原的甜品',
      ),

      // 坚果类菜品
      Dish(
        id: 'walnut_cake',
        name: '核桃蛋糕',
        category: '甜品',
        ingredients: ['核桃', '鸡蛋', '面粉', '糖'],
        allergens: ['核桃', '鸡蛋', '小麦'],
        description: '含坚果的蛋糕',
      ),
      Dish(
        id: 'almond_tofu',
        name: '杏仁豆腐',
        category: '甜品',
        ingredients: ['杏仁', '豆腐', '糖'],
        allergens: ['杏仁', '大豆'],
        description: '传统甜品',
      ),
      Dish(
        id: 'peanut_soup',
        name: '花生汤',
        category: '甜品',
        ingredients: ['花生', '糖', '水'],
        allergens: ['花生'],
        description: '香甜的花生汤',
      ),

      // 面食类
      Dish(
        id: 'sesame_noodles',
        name: '芝麻面条',
        category: '主食',
        ingredients: ['面条', '芝麻油', '酱油'],
        allergens: ['小麦', '芝麻'],
        description: '含芝麻的面食',
      ),
      Dish(
        id: 'egg_noodles',
        name: '鸡蛋面',
        category: '主食',
        ingredients: ['面条', '鸡蛋', '青菜'],
        allergens: ['小麦', '鸡蛋'],
        description: '含蛋的面条',
      ),

      // 更多虾类菜品
      Dish(
        id: 'fried_shrimp',
        name: '油焖虾',
        category: '海鲜',
        ingredients: ['虾', '料酒', '生抽', '糖'],
        allergens: ['虾'],
        description: '红烧口味的虾',
      ),
      Dish(
        id: 'shrimp_balls',
        name: '虾球',
        category: '海鲜',
        ingredients: ['虾仁', '淀粉', '蛋白'],
        allergens: ['虾', '鸡蛋'],
        description: 'Q弹的虾球',
      ),

      // 更多蟹类菜品
      Dish(
        id: 'crab_meat_tofu',
        name: '蟹肉豆腐',
        category: '海鲜',
        ingredients: ['蟹肉', '豆腐', '蛋白'],
        allergens: ['蟹', '大豆', '鸡蛋'],
        description: '鲜美的蟹肉豆腐',
      ),
      Dish(
        id: 'crab_roe_noodles',
        name: '蟹黄面',
        category: '主食',
        ingredients: ['面条', '蟹黄', '高汤'],
        allergens: ['小麦', '蟹'],
        description: '鲜美的蟹黄面条',
      ),
    ];
  }

  // 获取所有菜品
  static List<Dish> getAllDishes() {
    if (!_isInitialized) {
      initialize();
    }
    return _dishes;
  }

  // 获取所有配料
  static List<Ingredient> getAllIngredients() {
    if (!_isInitialized) {
      initialize();
    }
    return _ingredients;
  }

  // 根据菜名搜索菜品（增强版）
  static List<Dish> searchDishes(String dishName) {
    if (!_isInitialized) {
      initialize();
    }

    final query = _cleanSearchText(dishName);

    // 调试信息
    if (kDebugMode) {
      print('🔍 搜索菜品: "$dishName" -> "$query"');
    }

    final results = _dishes.where((dish) {
      final dishNameClean = _cleanSearchText(dish.name);

      // 1. 精确匹配
      if (dishNameClean == query) {
        if (kDebugMode) {
          print('  ✅ 精确匹配: ${dish.name}');
        }
        return true;
      }

      // 2. 包含匹配（双向）
      if (dishNameClean.contains(query) || query.contains(dishNameClean)) {
        if (kDebugMode) {
          print('  ✅ 包含匹配: ${dish.name}');
        }
        return true;
      }

      // 3. 模糊匹配（字符相似度）
      if (_calculateSimilarity(query, dishNameClean) > 0.7) {
        if (kDebugMode) {
          print('  ✅ 模糊匹配: ${dish.name}');
        }
        return true;
      }

      return false;
    }).toList();

    if (kDebugMode) {
      print('  搜索结果: ${results.map((d) => d.name).toList()}');
    }

    return results;
  }

  // 清洗搜索文本
  static String _cleanSearchText(String text) {
    String cleaned = text.toLowerCase().trim();

    // 去除空格
    cleaned = cleaned.replaceAll(' ', '');

    // 去除标点符号
    cleaned =
        cleaned.replaceAll(RegExp(r'[，。、；：！？""' '（）【】《》〈〉「」『』〔〕［］｛｝]'), '');
    cleaned = cleaned.replaceAll(RegExp(r'[,.;:!?"\(\)\[\]{}]'), '');

    // 去除数字和价格符号
    cleaned = cleaned.replaceAll(RegExp(r'[0-9￥¥元兀]'), '');

    return cleaned;
  }

  // 计算字符串相似度
  static double _calculateSimilarity(String s1, String s2) {
    if (s1.isEmpty || s2.isEmpty) return 0.0;
    if (s1 == s2) return 1.0;

    // 简单的字符匹配相似度
    int matches = 0;
    int minLength = s1.length < s2.length ? s1.length : s2.length;

    for (int i = 0; i < minLength; i++) {
      if (s1[i] == s2[i]) {
        matches++;
      }
    }

    return matches / (s1.length > s2.length ? s1.length : s2.length);
  }

  // 根据配料搜索菜品
  static List<Dish> searchDishesByIngredient(String ingredient) {
    if (!_isInitialized) {
      initialize();
    }

    return _dishes.where((dish) {
      return dish.ingredients.any((ing) => ing.contains(ingredient));
    }).toList();
  }

  // 获取菜品统计信息
  static Map<String, int> getDishStats() {
    if (!_isInitialized) {
      initialize();
    }

    final stats = <String, int>{};
    for (var dish in _dishes) {
      stats[dish.category] = (stats[dish.category] ?? 0) + 1;
    }
    return stats;
  }

  // 调试：打印所有菜品信息
  static void printAllDishes() {
    if (!_isInitialized) {
      initialize();
    }

    if (kDebugMode) {
      print('📚 数据库中的所有菜品:');
      for (int i = 0; i < _dishes.length; i++) {
        final dish = _dishes[i];
        final cleanName = _cleanSearchText(dish.name);
        print(
            '  ${i + 1}. "${dish.name}" -> "$cleanName" (过敏原: ${dish.allergens})');
        print('     字符编码: ${dish.name.codeUnits}');
      }
      print('📚 总计: ${_dishes.length} 道菜品');
    }
  }

  // 测试特定菜品的匹配
  static void testDishMatching(String testName) {
    if (!_isInitialized) {
      initialize();
    }

    if (kDebugMode) {
      print('🧪 测试菜品匹配: "$testName"');
      final cleanTest = _cleanSearchText(testName);
      print('  清洗后: "$cleanTest"');
      print('  字符编码: ${testName.codeUnits}');

      final matches = searchDishes(testName);
      if (matches.isNotEmpty) {
        print('  ✅ 找到匹配: ${matches.map((d) => d.name).toList()}');
      } else {
        print('  ❌ 未找到匹配');

        // 尝试逐个字符匹配
        print('  🔍 逐个检查数据库菜品:');
        for (var dish in _dishes) {
          final dishClean = _cleanSearchText(dish.name);
          final similarity = _calculateSimilarity(cleanTest, dishClean);
          if (similarity > 0.3) {
            print(
                '    "${dish.name}" -> "$dishClean" (相似度: ${(similarity * 100).toStringAsFixed(1)}%)');
          }
        }
      }
    }
  }
}
