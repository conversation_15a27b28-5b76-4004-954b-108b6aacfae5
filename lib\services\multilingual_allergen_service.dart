import 'package:flutter/foundation.dart';

class MultilingualAllergenService {
  
  /// 多语言过敏原映射表
  static const Map<String, Map<String, List<String>>> allergenTranslations = {
    // 海鲜类
    'seafood': {
      'zh': ['虾', '蟹', '鱼', '贝类', '海鲜', '龙虾', '扇贝', '牡蛎'],
      'en': ['shrimp', 'crab', 'fish', 'shellfish', 'seafood', 'lobster', 'scallop', 'oyster', 'prawns', 'salmon', 'tuna'],
      'de': ['garnelen', 'krabbe', 'fisch', 'schalentiere', 'meeresfrüchte', 'hummer', 'jakobsmuschel', 'auster', 'lachs', 'thunfisch'],
    },
    
    // 坚果类
    'nuts': {
      'zh': ['花生', '核桃', '杏仁', '坚果', '腰果', '榛子', '开心果', '松子'],
      'en': ['peanut', 'walnut', 'almond', 'nuts', 'cashew', 'hazelnut', 'pistachio', 'pine nuts', 'peanuts', 'tree nuts'],
      'de': ['erdnuss', 'walnuss', 'mandel', 'nüsse', 'cashew', 'haselnuss', 'pistazie', 'pinienkerne', 'erdnüsse', 'schalenfrüchte'],
    },
    
    // 乳制品
    'dairy': {
      'zh': ['牛奶', '奶酪', '乳制品', '黄油', '奶油', '酸奶', '奶粉'],
      'en': ['milk', 'cheese', 'dairy', 'butter', 'cream', 'yogurt', 'milk powder', 'lactose', 'whey'],
      'de': ['milch', 'käse', 'milchprodukte', 'butter', 'sahne', 'joghurt', 'milchpulver', 'laktose', 'molke'],
    },
    
    // 蛋类
    'eggs': {
      'zh': ['鸡蛋', '蛋', '蛋白', '蛋黄', '鸭蛋', '鹌鹑蛋'],
      'en': ['egg', 'eggs', 'egg white', 'egg yolk', 'duck egg', 'quail egg', 'mayonnaise'],
      'de': ['ei', 'eier', 'eiweiß', 'eigelb', 'entenei', 'wachtelei', 'mayonnaise'],
    },
    
    // 豆类
    'soy': {
      'zh': ['大豆', '豆腐', '豆浆', '豆制品', '黄豆', '豆瓣酱', '生抽', '老抽'],
      'en': ['soy', 'soybean', 'tofu', 'soy milk', 'soy products', 'soy sauce', 'miso', 'tempeh'],
      'de': ['soja', 'sojabohne', 'tofu', 'sojamilch', 'sojaprodukte', 'sojasauce', 'miso', 'tempeh'],
    },
    
    // 谷物类（含麸质）
    'gluten': {
      'zh': ['小麦', '面粉', '面条', '面包', '麸质', '大麦', '黑麦', '燕麦'],
      'en': ['wheat', 'flour', 'noodles', 'bread', 'gluten', 'barley', 'rye', 'oats', 'pasta'],
      'de': ['weizen', 'mehl', 'nudeln', 'brot', 'gluten', 'gerste', 'roggen', 'hafer', 'pasta'],
    },
    
    // 芝麻
    'sesame': {
      'zh': ['芝麻', '芝麻油', '香油', '芝麻酱', '黑芝麻', '白芝麻'],
      'en': ['sesame', 'sesame oil', 'sesame paste', 'tahini', 'black sesame', 'white sesame'],
      'de': ['sesam', 'sesamöl', 'sesampaste', 'tahini', 'schwarzer sesam', 'weißer sesam'],
    },
    
    // 味精
    'msg': {
      'zh': ['味精', 'MSG', '鸡精', '调味料', '谷氨酸钠'],
      'en': ['msg', 'monosodium glutamate', 'chicken powder', 'seasoning', 'glutamate'],
      'de': ['msg', 'mononatriumglutamat', 'hühnerpulver', 'würzmittel', 'glutamat'],
    },
  };

  /// 将检测到的配料转换为标准化过敏原
  static List<String> detectAllergensFromIngredients(
    List<String> ingredients, 
    String language
  ) {
    if (kDebugMode) {
      print('🔍 多语言过敏原检测: $ingredients (语言: $language)');
    }

    final detectedAllergens = <String>[];
    final ingredientsLower = ingredients.map((i) => i.toLowerCase()).toList();

    // 遍历所有过敏原类别
    for (final entry in allergenTranslations.entries) {
      final allergenType = entry.key;
      final translations = entry.value;
      
      // 获取当前语言的关键词
      final keywords = translations[language] ?? translations['en'] ?? [];
      
      // 检查是否有匹配
      bool hasMatch = false;
      for (final keyword in keywords) {
        for (final ingredient in ingredientsLower) {
          if (ingredient.contains(keyword.toLowerCase()) || 
              keyword.toLowerCase().contains(ingredient)) {
            hasMatch = true;
            break;
          }
        }
        if (hasMatch) break;
      }
      
      if (hasMatch) {
        detectedAllergens.add(allergenType);
        if (kDebugMode) {
          print('  ✅ 检测到过敏原: $allergenType');
        }
      }
    }

    return detectedAllergens;
  }

  /// 将过敏原类型转换为用户语言
  static List<String> translateAllergensToUserLanguage(
    List<String> allergenTypes, 
    String targetLanguage
  ) {
    final translatedAllergens = <String>[];

    for (final allergenType in allergenTypes) {
      final translations = allergenTranslations[allergenType];
      if (translations != null) {
        final targetWords = translations[targetLanguage] ?? translations['zh'] ?? [];
        if (targetWords.isNotEmpty) {
          translatedAllergens.add(targetWords.first); // 使用第一个作为主要翻译
        }
      }
    }

    return translatedAllergens;
  }

  /// 检查用户过敏原与检测结果的匹配
  static List<String> findMatchedAllergens(
    List<String> detectedAllergenTypes,
    List<String> userAllergenNames,
    String userLanguage
  ) {
    if (kDebugMode) {
      print('🔍 匹配用户过敏原:');
      print('  检测到的类型: $detectedAllergenTypes');
      print('  用户过敏原: $userAllergenNames');
      print('  用户语言: $userLanguage');
    }

    final matchedAllergens = <String>[];

    for (final allergenType in detectedAllergenTypes) {
      final translations = allergenTranslations[allergenType];
      if (translations == null) continue;

      // 获取该过敏原类型在用户语言下的所有可能表达
      final possibleNames = translations[userLanguage] ?? translations['zh'] ?? [];
      
      // 检查用户过敏原列表中是否有匹配
      for (final userAllergen in userAllergenNames) {
        for (final possibleName in possibleNames) {
          if (userAllergen.toLowerCase().contains(possibleName.toLowerCase()) ||
              possibleName.toLowerCase().contains(userAllergen.toLowerCase())) {
            if (!matchedAllergens.contains(userAllergen)) {
              matchedAllergens.add(userAllergen);
              if (kDebugMode) {
                print('  ✅ 匹配: $userAllergen ← $allergenType');
              }
            }
            break;
          }
        }
      }
    }

    return matchedAllergens;
  }

  /// 获取过敏原的多语言描述
  static Map<String, String> getAllergenDescription(String allergenType) {
    final descriptions = {
      'seafood': {
        'zh': '海鲜类过敏原，包括虾、蟹、鱼等',
        'en': 'Seafood allergens including shrimp, crab, fish, etc.',
        'de': 'Meeresfrüchte-Allergene einschließlich Garnelen, Krabben, Fisch usw.',
      },
      'nuts': {
        'zh': '坚果类过敏原，包括花生、核桃、杏仁等',
        'en': 'Nut allergens including peanuts, walnuts, almonds, etc.',
        'de': 'Nuss-Allergene einschließlich Erdnüsse, Walnüsse, Mandeln usw.',
      },
      'dairy': {
        'zh': '乳制品过敏原，包括牛奶、奶酪、黄油等',
        'en': 'Dairy allergens including milk, cheese, butter, etc.',
        'de': 'Milchprodukt-Allergene einschließlich Milch, Käse, Butter usw.',
      },
      'eggs': {
        'zh': '蛋类过敏原，包括鸡蛋及蛋制品',
        'en': 'Egg allergens including chicken eggs and egg products',
        'de': 'Ei-Allergene einschließlich Hühnereier und Eiprodukte',
      },
      'soy': {
        'zh': '豆类过敏原，包括大豆及豆制品',
        'en': 'Soy allergens including soybeans and soy products',
        'de': 'Soja-Allergene einschließlich Sojabohnen und Sojaprodukte',
      },
      'gluten': {
        'zh': '麸质过敏原，包括小麦、大麦、黑麦等',
        'en': 'Gluten allergens including wheat, barley, rye, etc.',
        'de': 'Gluten-Allergene einschließlich Weizen, Gerste, Roggen usw.',
      },
      'sesame': {
        'zh': '芝麻过敏原，包括芝麻及芝麻制品',
        'en': 'Sesame allergens including sesame and sesame products',
        'de': 'Sesam-Allergene einschließlich Sesam und Sesamprodukte',
      },
      'msg': {
        'zh': '味精过敏原，包括谷氨酸钠等',
        'en': 'MSG allergens including monosodium glutamate',
        'de': 'MSG-Allergene einschließlich Mononatriumglutamat',
      },
    };

    return descriptions[allergenType] ?? {
      'zh': '未知过敏原',
      'en': 'Unknown allergen',
      'de': 'Unbekanntes Allergen',
    };
  }

  /// 获取建议的替代配料
  static List<String> getSafeAlternatives(
    String allergenType, 
    String language
  ) {
    final alternatives = {
      'seafood': {
        'zh': ['鸡肉', '猪肉', '牛肉', '豆腐', '蘑菇'],
        'en': ['chicken', 'pork', 'beef', 'tofu', 'mushrooms'],
        'de': ['hähnchen', 'schweinefleisch', 'rindfleisch', 'tofu', 'pilze'],
      },
      'nuts': {
        'zh': ['葵花籽', '南瓜籽', '芝麻', '椰子'],
        'en': ['sunflower seeds', 'pumpkin seeds', 'sesame', 'coconut'],
        'de': ['sonnenblumenkerne', 'kürbiskerne', 'sesam', 'kokosnuss'],
      },
      'dairy': {
        'zh': ['豆浆', '椰奶', '杏仁奶', '燕麦奶'],
        'en': ['soy milk', 'coconut milk', 'almond milk', 'oat milk'],
        'de': ['sojamilch', 'kokosmilch', 'mandelmilch', 'hafermilch'],
      },
      'eggs': {
        'zh': ['豆腐', '香蕉', '亚麻籽', '苹果酱'],
        'en': ['tofu', 'banana', 'flax seeds', 'applesauce'],
        'de': ['tofu', 'banane', 'leinsamen', 'apfelmus'],
      },
      'soy': {
        'zh': ['椰子氨基酸', '盐', '香草', '香料'],
        'en': ['coconut aminos', 'salt', 'herbs', 'spices'],
        'de': ['kokos-aminos', 'salz', 'kräuter', 'gewürze'],
      },
      'gluten': {
        'zh': ['米粉', '玉米粉', '土豆粉', '杏仁粉'],
        'en': ['rice flour', 'corn flour', 'potato flour', 'almond flour'],
        'de': ['reismehl', 'maismehl', 'kartoffelmehl', 'mandelmehl'],
      },
    };

    return alternatives[allergenType]?[language] ?? 
           alternatives[allergenType]?['en'] ?? 
           [];
  }

  /// 生成多语言警告信息
  static String generateWarning(
    List<String> matchedAllergens,
    String language
  ) {
    if (matchedAllergens.isEmpty) {
      final noAllergenMessages = {
        'zh': '未检测到您设置的过敏原',
        'en': 'No allergens detected that match your settings',
        'de': 'Keine Allergene erkannt, die Ihren Einstellungen entsprechen',
      };
      return noAllergenMessages[language] ?? noAllergenMessages['zh']!;
    }

    final warningTemplates = {
      'zh': '⚠️ 警告：此菜品可能含有以下过敏原：${matchedAllergens.join('、')}',
      'en': '⚠️ Warning: This dish may contain the following allergens: ${matchedAllergens.join(', ')}',
      'de': '⚠️ Warnung: Dieses Gericht kann folgende Allergene enthalten: ${matchedAllergens.join(', ')}',
    };

    return warningTemplates[language] ?? warningTemplates['zh']!;
  }
}
