@echo off
echo === Web版故障排除工具 ===
echo.

echo 1. 检查Flutter Web支持...
flutter config --enable-web
echo.

echo 2. 检查可用设备...
flutter devices
echo.

echo 3. 清理项目...
flutter clean
echo.

echo 4. 重新获取依赖...
flutter pub get
echo.

echo 5. 尝试不同端口启动Web服务器...
echo.

echo 尝试端口 3000...
flutter run -d web-server --web-port 3000 --web-hostname localhost
if %ERRORLEVEL% NEQ 0 (
    echo.
    echo 端口3000失败，尝试端口4000...
    flutter run -d web-server --web-port 4000 --web-hostname localhost
    if %ERRORLEVEL% NEQ 0 (
        echo.
        echo 端口4000失败，尝试端口5000...
        flutter run -d web-server --web-port 5000 --web-hostname localhost
        if %ERRORLEVEL% NEQ 0 (
            echo.
            echo 所有端口都失败，请检查防火墙设置或以管理员身份运行
            pause
        )
    )
)

pause
