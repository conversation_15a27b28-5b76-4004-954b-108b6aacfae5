@echo off
echo === 修复Gradle问题 ===
echo.

echo 1. 清理Flutter缓存...
flutter clean

echo.
echo 2. 清理Gradle缓存...
if exist "%USERPROFILE%\.gradle" (
    echo 删除Gradle缓存目录...
    rmdir /s /q "%USERPROFILE%\.gradle"
    echo Gradle缓存已清理
) else (
    echo Gradle缓存目录不存在
)

echo.
echo 3. 清理Android构建缓存...
if exist "android\.gradle" (
    rmdir /s /q "android\.gradle"
    echo Android构建缓存已清理
)

if exist "android\app\build" (
    rmdir /s /q "android\app\build"
    echo Android应用构建缓存已清理
)

echo.
echo 4. 重新获取依赖...
flutter pub get

echo.
echo 5. 尝试重新构建...
echo 如果网络较慢，这可能需要几分钟时间...
flutter build apk --debug

echo.
echo 修复完成！现在可以尝试运行：
echo flutter run
echo.
pause
