import 'dart:convert';
import 'package:flutter/foundation.dart';

class StructuredDataService {
  
  /// 菜单结构化数据模型
  static Map<String, dynamic> convertToStructuredData(
    List<String> ocrLines,
    String detectedLanguage,
  ) {
    if (kDebugMode) {
      print('📊 开始结构化数据转换...');
      print('🌍 检测语言: $detectedLanguage');
    }

    final Map<String, dynamic> structuredData = {
      'metadata': {
        'language': detectedLanguage,
        'processing_timestamp': DateTime.now().toIso8601String(),
        'total_lines': ocrLines.length,
        'confidence_score': 0.0,
      },
      'menu_sections': <Map<String, dynamic>>[],
      'dishes': <Map<String, dynamic>>[],
      'ingredients': <String>[],
      'allergens': <String>[],
      'prices': <Map<String, dynamic>>[],
      'layout_analysis': <String, dynamic>{},
    };

    // 1. 布局分析
    final layoutAnalysis = _analyzeLayout(ocrLines, detectedLanguage);
    structuredData['layout_analysis'] = layoutAnalysis;

    // 2. 实体识别
    final entities = _extractEntities(ocrLines, detectedLanguage);
    structuredData['dishes'] = entities['dishes'];
    structuredData['ingredients'] = entities['ingredients'];
    structuredData['prices'] = entities['prices'];

    // 3. 上下文理解和过敏原检测
    final allergenInfo = _detectAllergens(ocrLines, detectedLanguage);
    structuredData['allergens'] = allergenInfo['detected_allergens'];
    structuredData['allergen_warnings'] = allergenInfo['warnings'];

    // 4. 菜单分区
    final sections = _identifyMenuSections(ocrLines, detectedLanguage);
    structuredData['menu_sections'] = sections;

    // 5. 计算整体置信度
    structuredData['metadata']['confidence_score'] = _calculateConfidenceScore(structuredData);

    if (kDebugMode) {
      print('✅ 结构化数据转换完成');
      print('📋 识别到 ${structuredData['dishes'].length} 道菜品');
      print('🥗 识别到 ${structuredData['ingredients'].length} 种配料');
      print('⚠️ 识别到 ${structuredData['allergens'].length} 种过敏原');
    }

    return structuredData;
  }

  /// 布局分析：识别菜单中的不同区域
  static Map<String, dynamic> _analyzeLayout(List<String> lines, String language) {
    if (kDebugMode) {
      print('🔍 进行布局分析...');
    }

    final Map<String, dynamic> layout = {
      'header_lines': <int>[],
      'dish_lines': <int>[],
      'price_lines': <int>[],
      'description_lines': <int>[],
      'footer_lines': <int>[],
      'column_structure': 'unknown',
    };

    for (int i = 0; i < lines.length; i++) {
      final String line = lines[i].trim();
      if (line.isEmpty) continue;

      // 检测标题行（通常在顶部，字体较大，居中）
      if (i < 3 && _isHeaderLine(line, language)) {
        layout['header_lines'].add(i);
      }
      // 检测价格行
      else if (_containsPrice(line, language)) {
        layout['price_lines'].add(i);
      }
      // 检测菜品名称行
      else if (_isDishNameLine(line, language)) {
        layout['dish_lines'].add(i);
      }
      // 检测描述行
      else if (_isDescriptionLine(line, language)) {
        layout['description_lines'].add(i);
      }
      // 检测页脚行
      else if (i > lines.length - 3 && _isFooterLine(line, language)) {
        layout['footer_lines'].add(i);
      }
    }

    // 分析列结构
    layout['column_structure'] = _analyzeColumnStructure(lines);

    return layout;
  }

  /// 实体识别：从文本中识别关键实体
  static Map<String, dynamic> _extractEntities(List<String> lines, String language) {
    if (kDebugMode) {
      print('🎯 进行实体识别...');
    }

    final Map<String, dynamic> entities = {
      'dishes': <Map<String, dynamic>>[],
      'ingredients': <String>[],
      'prices': <Map<String, dynamic>>[],
      'cooking_methods': <String>[],
    };

    for (int i = 0; i < lines.length; i++) {
      final String line = lines[i].trim();
      if (line.isEmpty) continue;

      // 提取菜品信息
      final dishInfo = _extractDishInfo(line, language, i);
      if (dishInfo != null) {
        entities['dishes'].add(dishInfo);
      }

      // 提取配料信息
      final ingredients = _extractIngredients(line, language);
      entities['ingredients'].addAll(ingredients);

      // 提取价格信息
      final priceInfo = _extractPriceInfo(line, language, i);
      if (priceInfo != null) {
        entities['prices'][i.toString()] = priceInfo;
      }

      // 提取烹饪方式
      final cookingMethods = _extractCookingMethods(line, language);
      entities['cooking_methods'].addAll(cookingMethods);
    }

    // 去重
    entities['ingredients'] = (entities['ingredients'] as List).toSet().toList();
    entities['cooking_methods'] = (entities['cooking_methods'] as List).toSet().toList();

    return entities;
  }

  /// 过敏原检测和上下文理解
  static Map<String, dynamic> _detectAllergens(List<String> lines, String language) {
    if (kDebugMode) {
      print('⚠️ 进行过敏原检测...');
    }

    final Map<String, dynamic> allergenInfo = {
      'detected_allergens': <String>[],
      'warnings': <Map<String, dynamic>>[],
      'explicit_mentions': <String>[],
      'implicit_allergens': <String>[],
    };

    // 过敏原关键词（多语言）
    final Map<String, List<String>> allergenKeywords = {
      'en': [
        'contains nuts', 'contains dairy', 'contains gluten', 'contains eggs',
        'contains soy', 'contains shellfish', 'contains fish', 'contains sesame',
        'may contain', 'allergen warning', 'gluten-free', 'dairy-free',
        'nut-free', 'vegan', 'vegetarian'
      ],
      'de': [
        'enthält nüsse', 'enthält milch', 'enthält gluten', 'enthält eier',
        'enthält soja', 'enthält schalentiere', 'enthält fisch', 'enthält sesam',
        'kann enthalten', 'allergen warnung', 'glutenfrei', 'milchfrei',
        'nussfrei', 'vegan', 'vegetarisch'
      ],
      'zh': [
        '含有坚果', '含有乳制品', '含有麸质', '含有鸡蛋',
        '含有大豆', '含有贝类', '含有鱼类', '含有芝麻',
        '可能含有', '过敏原警告', '无麸质', '无乳制品',
        '无坚果', '素食', '纯素'
      ],
    };

    final List<String> keywords = allergenKeywords[language] ?? allergenKeywords['en']!;

    for (int i = 0; i < lines.length; i++) {
      final String line = lines[i].toLowerCase();

      // 检测明确的过敏原提及
      for (String keyword in keywords) {
        if (line.contains(keyword.toLowerCase())) {
          allergenInfo['explicit_mentions'].add(keyword);
          
          final Map<String, dynamic> warning = {
            'line_number': i,
            'text': lines[i],
            'allergen_type': _categorizeAllergen(keyword),
            'confidence': 0.9,
          };
          allergenInfo['warnings'].add(warning);
        }
      }

      // 检测隐含的过敏原（通过配料推断）
      final implicitAllergens = _detectImplicitAllergens(line, language);
      allergenInfo['implicit_allergens'].addAll(implicitAllergens);
    }

    // 合并所有检测到的过敏原
    final Set<String> allAllergens = {};
    allAllergens.addAll(allergenInfo['explicit_mentions']);
    allAllergens.addAll(allergenInfo['implicit_allergens']);
    allergenInfo['detected_allergens'] = allAllergens.toList();

    return allergenInfo;
  }

  /// 识别菜单分区
  static List<Map<String, dynamic>> _identifyMenuSections(List<String> lines, String language) {
    if (kDebugMode) {
      print('📑 识别菜单分区...');
    }

    final List<Map<String, dynamic>> sections = [];
    
    // 常见菜单分区关键词
    final Map<String, List<String>> sectionKeywords = {
      'en': [
        'appetizers', 'starters', 'main course', 'entrees', 'desserts',
        'beverages', 'drinks', 'salads', 'soups', 'pasta', 'pizza',
        'seafood', 'meat', 'vegetarian', 'specials'
      ],
      'de': [
        'vorspeisen', 'hauptgerichte', 'nachspeisen', 'getränke',
        'salate', 'suppen', 'pasta', 'pizza', 'meeresfrüchte',
        'fleisch', 'vegetarisch', 'spezialitäten'
      ],
      'zh': [
        '开胃菜', '前菜', '主菜', '甜品', '饮料',
        '沙拉', '汤', '面食', '海鲜', '肉类',
        '素食', '特色菜', '招牌菜'
      ],
    };

    final List<String> keywords = sectionKeywords[language] ?? sectionKeywords['en']!;
    
    String currentSection = 'unknown';
    int sectionStart = 0;

    for (int i = 0; i < lines.length; i++) {
      final String line = lines[i].toLowerCase().trim();
      
      // 检测分区标题
      for (String keyword in keywords) {
        if (line.contains(keyword.toLowerCase())) {
          // 保存前一个分区
          if (i > sectionStart) {
            sections.add({
              'name': currentSection,
              'start_line': sectionStart,
              'end_line': i - 1,
              'lines': lines.sublist(sectionStart, i),
            });
          }
          
          currentSection = keyword;
          sectionStart = i;
          break;
        }
      }
    }

    // 添加最后一个分区
    if (sectionStart < lines.length) {
      sections.add({
        'name': currentSection,
        'start_line': sectionStart,
        'end_line': lines.length - 1,
        'lines': lines.sublist(sectionStart),
      });
    }

    return sections;
  }

  /// 计算整体置信度分数
  static double _calculateConfidenceScore(Map<String, dynamic> data) {
    double score = 0.0;
    int factors = 0;

    // 基于识别到的实体数量
    if (data['dishes'].length > 0) {
      score += 0.3;
      factors++;
    }

    if (data['ingredients'].length > 0) {
      score += 0.2;
      factors++;
    }

    if (data['prices'].isNotEmpty) {
      score += 0.2;
      factors++;
    }

    if (data['menu_sections'].length > 1) {
      score += 0.2;
      factors++;
    }

    if (data['allergens'].length > 0) {
      score += 0.1;
      factors++;
    }

    return factors > 0 ? score : 0.0;
  }

  // 辅助方法
  static bool _isHeaderLine(String line, String language) {
    final headerPatterns = {
      'en': ['menu', 'restaurant', 'cafe', 'bistro', 'kitchen'],
      'de': ['speisekarte', 'restaurant', 'cafe', 'bistro', 'küche'],
      'zh': ['菜单', '菜谱', '餐厅', '酒店', '厨房'],
    };
    
    final patterns = headerPatterns[language] ?? headerPatterns['en']!;
    return patterns.any((pattern) => line.toLowerCase().contains(pattern));
  }

  static bool _containsPrice(String line, String language) {
    final pricePatterns = {
      'en': [r'\$\d+', r'£\d+', r'€\d+'],
      'de': [r'€\d+', r'\d+,\d+\s*€'],
      'zh': [r'¥\d+', r'￥\d+', r'\d+元'],
    };
    
    final patterns = pricePatterns[language] ?? pricePatterns['en']!;
    return patterns.any((pattern) => RegExp(pattern).hasMatch(line));
  }

  static bool _isDishNameLine(String line, String language) {
    // 简化的菜品名称检测
    return line.length > 3 && line.length < 50 && !_containsPrice(line, language);
  }

  static bool _isDescriptionLine(String line, String language) {
    return line.length > 20 && !_containsPrice(line, language);
  }

  static bool _isFooterLine(String line, String language) {
    final footerPatterns = {
      'en': ['thank you', 'visit us', 'contact', 'address'],
      'de': ['danke', 'besuchen sie uns', 'kontakt', 'adresse'],
      'zh': ['谢谢', '欢迎光临', '联系', '地址'],
    };
    
    final patterns = footerPatterns[language] ?? footerPatterns['en']!;
    return patterns.any((pattern) => line.toLowerCase().contains(pattern));
  }

  static String _analyzeColumnStructure(List<String> lines) {
    // 简化的列结构分析
    int singleColumnLines = 0;
    int multiColumnLines = 0;
    
    for (String line in lines) {
      if (line.contains('\t') || line.split(RegExp(r'\s{3,}')).length > 1) {
        multiColumnLines++;
      } else {
        singleColumnLines++;
      }
    }
    
    return multiColumnLines > singleColumnLines ? 'multi_column' : 'single_column';
  }

  static Map<String, dynamic>? _extractDishInfo(String line, String language, int lineNumber) {
    // 简化的菜品信息提取
    if (line.length < 3 || line.length > 100) return null;
    
    return {
      'name': line.trim(),
      'line_number': lineNumber,
      'language': language,
      'confidence': 0.8,
    };
  }

  static List<String> _extractIngredients(String line, String language) {
    // 简化的配料提取
    final ingredientPatterns = {
      'en': ['chicken', 'beef', 'pork', 'fish', 'cheese', 'tomato', 'onion'],
      'de': ['hähnchen', 'rindfleisch', 'schweinefleisch', 'fisch', 'käse'],
      'zh': ['鸡肉', '牛肉', '猪肉', '鱼', '奶酪', '番茄', '洋葱'],
    };
    
    final patterns = ingredientPatterns[language] ?? ingredientPatterns['en']!;
    final List<String> found = [];
    
    for (String ingredient in patterns) {
      if (line.toLowerCase().contains(ingredient.toLowerCase())) {
        found.add(ingredient);
      }
    }
    
    return found;
  }

  static Map<String, dynamic>? _extractPriceInfo(String line, String language, int lineNumber) {
    final priceRegex = RegExp(r'[\$£€¥￥]\d+(?:[.,]\d{2})?|\d+(?:[.,]\d{2})?\s*[\$£€¥￥元]');
    final match = priceRegex.firstMatch(line);
    
    if (match != null) {
      return {
        'price_text': match.group(0),
        'line_number': lineNumber,
        'currency': _extractCurrency(match.group(0)!),
      };
    }
    
    return null;
  }

  static List<String> _extractCookingMethods(String line, String language) {
    final cookingMethods = {
      'en': ['grilled', 'fried', 'baked', 'steamed', 'roasted', 'sauteed'],
      'de': ['gegrillt', 'gebraten', 'gebacken', 'gedämpft', 'geröstet'],
      'zh': ['烤', '炸', '蒸', '炒', '煮', '焖'],
    };
    
    final methods = cookingMethods[language] ?? cookingMethods['en']!;
    final List<String> found = [];
    
    for (String method in methods) {
      if (line.toLowerCase().contains(method.toLowerCase())) {
        found.add(method);
      }
    }
    
    return found;
  }

  static List<String> _detectImplicitAllergens(String line, String language) {
    // 通过配料推断过敏原
    final allergenMap = {
      'en': {
        'nuts': ['almond', 'walnut', 'peanut', 'cashew'],
        'dairy': ['milk', 'cheese', 'butter', 'cream'],
        'gluten': ['wheat', 'bread', 'pasta', 'flour'],
        'eggs': ['egg', 'mayonnaise'],
      },
      'de': {
        'nüsse': ['mandel', 'walnuss', 'erdnuss', 'cashew'],
        'milch': ['milch', 'käse', 'butter', 'sahne'],
        'gluten': ['weizen', 'brot', 'pasta', 'mehl'],
        'eier': ['ei', 'mayonnaise'],
      },
      'zh': {
        '坚果': ['杏仁', '核桃', '花生', '腰果'],
        '乳制品': ['牛奶', '奶酪', '黄油', '奶油'],
        '麸质': ['小麦', '面包', '面条', '面粉'],
        '鸡蛋': ['鸡蛋', '蛋黄酱'],
      },
    };
    
    final allergens = allergenMap[language] ?? allergenMap['en']!;
    final List<String> detected = [];
    
    allergens.forEach((allergen, ingredients) {
      for (String ingredient in ingredients) {
        if (line.toLowerCase().contains(ingredient.toLowerCase())) {
          detected.add(allergen);
          break;
        }
      }
    });
    
    return detected;
  }

  static String _categorizeAllergen(String keyword) {
    if (keyword.contains('nut')) return 'nuts';
    if (keyword.contains('dairy') || keyword.contains('milk')) return 'dairy';
    if (keyword.contains('gluten')) return 'gluten';
    if (keyword.contains('egg')) return 'eggs';
    if (keyword.contains('soy')) return 'soy';
    if (keyword.contains('shellfish')) return 'shellfish';
    if (keyword.contains('fish')) return 'fish';
    if (keyword.contains('sesame')) return 'sesame';
    return 'other';
  }

  static String _extractCurrency(String priceText) {
    if (priceText.contains('\$')) return 'USD';
    if (priceText.contains('£')) return 'GBP';
    if (priceText.contains('€')) return 'EUR';
    if (priceText.contains('¥') || priceText.contains('￥')) return 'CNY';
    if (priceText.contains('元')) return 'CNY';
    return 'unknown';
  }

  /// 将结构化数据转换为JSON
  static String toJson(Map<String, dynamic> structuredData) {
    return jsonEncode(structuredData);
  }

  /// 从JSON恢复结构化数据
  static Map<String, dynamic> fromJson(String jsonString) {
    return jsonDecode(jsonString);
  }
}
