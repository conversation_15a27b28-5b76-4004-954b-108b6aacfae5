{"buildFiles": ["C:\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Project\\002_HyperV_Azure_OCR\\android\\app\\.cxx\\RelWithDebInfo\\265w4b3t\\x86", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Project\\002_HyperV_Azure_OCR\\android\\app\\.cxx\\RelWithDebInfo\\265w4b3t\\x86", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}